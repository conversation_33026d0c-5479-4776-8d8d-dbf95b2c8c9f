package com.baosight.rtservice.filter.core.api;

import com.baosight.rtservice.filter.core.model.dto.FilterContext;
import com.baosight.rtservice.filter.core.model.dto.FilterResult;

import java.util.Map;

/**
 * 过滤规则插件接口
 * 所有标准插件都需要实现此接口
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface FilterRulePlugin {
    
    /**
     * 执行过滤逻辑
     * 
     * @param context 过滤上下文，包含请求信息和环境变量
     * @param config 插件配置参数
     * @return 过滤结果
     */
    FilterResult execute(FilterContext context, Map<String, Object> config);
    
    /**
     * 获取插件ID
     * 
     * @return 插件唯一标识
     */
    String getPluginId();
    
    /**
     * 获取插件名称
     * 
     * @return 插件显示名称
     */
    String getPluginName();
    
    /**
     * 获取插件版本
     * 
     * @return 插件版本号
     */
    default String getVersion() {
        return "1.0.0";
    }
    
    /**
     * 验证配置参数
     * 
     * @param config 配置参数
     * @return 验证结果，null表示验证通过，非null表示错误信息
     */
    default String validateConfig(Map<String, Object> config) {
        return null;
    }
    
    /**
     * 获取配置模板
     * 
     * @return 配置参数的JSON模板
     */
    default String getConfigTemplate() {
        return "{}";
    }
}
