package com.baosight.rtservice.filter.core.exception;

/**
 * 插件异常
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class PluginException extends FilterSystemException {
    
    private final String pluginId;
    
    public PluginException(String pluginId, String message) {
        super(message);
        this.pluginId = pluginId;
    }
    
    public PluginException(String pluginId, String message, Throwable cause) {
        super(message, cause);
        this.pluginId = pluginId;
    }
    
    public PluginException(String pluginId, String errorCode, String message) {
        super(errorCode, message);
        this.pluginId = pluginId;
    }
    
    public PluginException(String pluginId, String errorCode, String message, Throwable cause) {
        super(errorCode, message, cause);
        this.pluginId = pluginId;
    }
    
    public String getPluginId() {
        return pluginId;
    }
}
