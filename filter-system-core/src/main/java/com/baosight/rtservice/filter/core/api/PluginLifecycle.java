package com.baosight.rtservice.filter.core.api;

/**
 * 插件生命周期接口
 * 插件可以实现此接口来处理初始化和销毁逻辑
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface PluginLifecycle {
    
    /**
     * 插件初始化
     * 在插件加载后调用
     * 
     * @throws Exception 初始化异常
     */
    default void initialize() throws Exception {
        // 默认空实现
    }
    
    /**
     * 插件销毁
     * 在插件卸载前调用
     * 
     * @throws Exception 销毁异常
     */
    default void destroy() throws Exception {
        // 默认空实现
    }
    
    /**
     * 插件启动
     * 在插件启用时调用
     * 
     * @throws Exception 启动异常
     */
    default void start() throws Exception {
        // 默认空实现
    }
    
    /**
     * 插件停止
     * 在插件禁用时调用
     * 
     * @throws Exception 停止异常
     */
    default void stop() throws Exception {
        // 默认空实现
    }
    
    /**
     * 健康检查
     * 检查插件是否正常运行
     * 
     * @return true表示健康，false表示异常
     */
    default boolean isHealthy() {
        return true;
    }
}
