package com.baosight.rtservice.filter.core.annotation;

import java.lang.annotation.*;

/**
 * 插件方法注解
 * 用于标识自定义插件的执行方法
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface PluginMethod {
    
    /**
     * 方法名称
     */
    String name() default "";
    
    /**
     * 方法描述
     */
    String description() default "";
    
    /**
     * 是否为主执行方法
     */
    boolean primary() default false;
    
    /**
     * 方法超时时间（毫秒）
     */
    long timeout() default 3000L;
}
