package com.baosight.rtservice.filter.core.api;

import java.util.Map;

/**
 * 配置验证接口
 * 用于验证插件配置参数的有效性
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface ConfigValidator {
    
    /**
     * 验证配置参数
     * 
     * @param config 配置参数
     * @return 验证结果
     */
    ValidationResult validate(Map<String, Object> config);
    
    /**
     * 验证结果
     */
    class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        private final String errorCode;
        
        private ValidationResult(boolean valid, String errorMessage, String errorCode) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.errorCode = errorCode;
        }
        
        /**
         * 创建成功的验证结果
         */
        public static ValidationResult success() {
            return new ValidationResult(true, null, null);
        }
        
        /**
         * 创建失败的验证结果
         */
        public static ValidationResult failure(String errorMessage) {
            return new ValidationResult(false, errorMessage, null);
        }
        
        /**
         * 创建失败的验证结果（带错误码）
         */
        public static ValidationResult failure(String errorMessage, String errorCode) {
            return new ValidationResult(false, errorMessage, errorCode);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public String getErrorCode() {
            return errorCode;
        }
    }
}
