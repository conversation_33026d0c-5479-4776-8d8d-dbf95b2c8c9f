package com.baosight.rtservice.filter.core.model.dto;

import com.baosight.rtservice.filter.core.model.enums.PluginStatus;
import com.baosight.rtservice.filter.core.model.enums.PluginType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 插件元数据
 * 包含插件的基本信息和状态
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PluginMetadata {
    
    /**
     * 插件ID
     */
    private String pluginId;
    
    /**
     * 插件名称
     */
    private String pluginName;
    
    /**
     * 插件描述
     */
    private String description;
    
    /**
     * 插件版本
     */
    private String version;
    
    /**
     * 插件作者
     */
    private String author;
    
    /**
     * 插件类型
     */
    private PluginType pluginType;
    
    /**
     * 插件状态
     */
    private PluginStatus status;
    
    /**
     * 插件类名
     */
    private String pluginClass;
    
    /**
     * 插件方法名（用于自定义方法插件）
     */
    private String pluginMethod;
    
    /**
     * 插件优先级
     */
    private Integer priority;
    
    /**
     * 插件超时时间（毫秒）
     */
    private Long timeout;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 最后执行时间
     */
    private LocalDateTime lastExecuteTime;
    
    /**
     * 执行次数
     */
    private Long executeCount;
    
    /**
     * 成功次数
     */
    private Long successCount;
    
    /**
     * 失败次数
     */
    private Long failureCount;
    
    /**
     * 平均执行时间（毫秒）
     */
    private Double avgExecutionTime;
    
    /**
     * 配置模板
     */
    private String configTemplate;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> properties;
    
    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (executeCount == null || executeCount == 0) {
            return 0.0;
        }
        return (double) (successCount != null ? successCount : 0) / executeCount * 100;
    }
    
    /**
     * 是否健康
     */
    public boolean isHealthy() {
        return status == PluginStatus.ACTIVE && enabled != null && enabled;
    }
}
