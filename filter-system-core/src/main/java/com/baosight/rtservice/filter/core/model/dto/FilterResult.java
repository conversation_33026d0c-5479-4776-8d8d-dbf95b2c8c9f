package com.baosight.rtservice.filter.core.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 过滤结果
 * 表示插件执行的结果
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilterResult {
    
    /**
     * 是否通过过滤
     */
    private boolean passed;
    
    /**
     * 拒绝原因
     */
    private String rejectReason;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 执行时间（毫秒）
     */
    private long executionTime;
    
    /**
     * 插件ID
     */
    private String pluginId;
    
    /**
     * 规则ID
     */
    private String ruleId;
    
    /**
     * 执行时间戳
     */
    private LocalDateTime executeTime;
    
    /**
     * 扩展信息
     */
    private Map<String, Object> metadata;
    
    /**
     * 创建通过的结果
     */
    public static FilterResult pass() {
        return FilterResult.builder()
                .passed(true)
                .executeTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建拒绝的结果
     */
    public static FilterResult reject(String reason) {
        return FilterResult.builder()
                .passed(false)
                .rejectReason(reason)
                .executeTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建拒绝的结果（带错误码）
     */
    public static FilterResult reject(String reason, String errorCode) {
        return FilterResult.builder()
                .passed(false)
                .rejectReason(reason)
                .errorCode(errorCode)
                .executeTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 获取扩展信息
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
    
    /**
     * 设置扩展信息
     */
    public void setMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new java.util.HashMap<>();
        }
        metadata.put(key, value);
    }
}
