package com.baosight.rtservice.filter.core.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 过滤上下文
 * 包含过滤请求的所有相关信息
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilterContext {
    
    /**
     * 请求ID，用于追踪
     */
    private String requestId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户角色
     */
    private String userRole;
    
    /**
     * 消息内容
     */
    private String messageContent;
    
    /**
     * 消息类型
     */
    private String messageType;
    
    /**
     * 发送方ID
     */
    private String senderId;
    
    /**
     * 接收方ID
     */
    private String receiverId;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 设备类型
     */
    private String deviceType;
    
    /**
     * 地理位置信息
     */
    private String location;
    
    /**
     * 请求时间
     */
    private LocalDateTime requestTime;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;
    
    /**
     * 获取扩展属性
     */
    public Object getAttribute(String key) {
        return attributes != null ? attributes.get(key) : null;
    }
    
    /**
     * 设置扩展属性
     */
    public void setAttribute(String key, Object value) {
        if (attributes == null) {
            attributes = new java.util.HashMap<>();
        }
        attributes.put(key, value);
    }
}
