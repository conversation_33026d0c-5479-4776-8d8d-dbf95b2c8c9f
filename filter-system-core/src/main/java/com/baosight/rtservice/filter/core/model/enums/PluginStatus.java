package com.baosight.rtservice.filter.core.model.enums;

/**
 * 插件状态枚举
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public enum PluginStatus {
    
    /**
     * 初始化中
     */
    INITIALIZING("initializing", "初始化中"),
    
    /**
     * 活跃状态
     */
    ACTIVE("active", "活跃"),
    
    /**
     * 已停用
     */
    INACTIVE("inactive", "已停用"),
    
    /**
     * 错误状态
     */
    ERROR("error", "错误"),
    
    /**
     * 已卸载
     */
    UNLOADED("unloaded", "已卸载");
    
    private final String code;
    private final String description;
    
    PluginStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static PluginStatus fromCode(String code) {
        for (PluginStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown plugin status code: " + code);
    }
    
    /**
     * 是否为可执行状态
     */
    public boolean isExecutable() {
        return this == ACTIVE;
    }
}
