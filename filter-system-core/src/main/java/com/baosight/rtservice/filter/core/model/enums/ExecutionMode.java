package com.baosight.rtservice.filter.core.model.enums;

/**
 * 执行模式枚举
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public enum ExecutionMode {
    
    /**
     * 责任链模式
     * 按顺序执行，遇到拒绝立即停止
     */
    CHAIN("chain", "责任链模式"),
    
    /**
     * 逻辑运算模式
     * 支持AND、OR、NOT等逻辑运算
     */
    LOGIC("logic", "逻辑运算模式"),
    
    /**
     * 并行模式
     * 所有规则并行执行
     */
    PARALLEL("parallel", "并行模式"),
    
    /**
     * 顺序模式
     * 按顺序执行所有规则
     */
    SEQUENTIAL("sequential", "顺序模式");
    
    private final String code;
    private final String description;
    
    ExecutionMode(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static ExecutionMode fromCode(String code) {
        for (ExecutionMode mode : values()) {
            if (mode.code.equals(code)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("Unknown execution mode code: " + code);
    }
}
