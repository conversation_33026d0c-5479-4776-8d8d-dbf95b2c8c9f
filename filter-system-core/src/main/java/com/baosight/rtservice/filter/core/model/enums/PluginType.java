package com.baosight.rtservice.filter.core.model.enums;

/**
 * 插件类型枚举
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public enum PluginType {
    
    /**
     * 内置插件
     */
    BUILTIN("builtin", "内置插件"),
    
    /**
     * 自定义插件
     */
    CUSTOM("custom", "自定义插件"),
    
    /**
     * 第三方插件
     */
    THIRD_PARTY("third_party", "第三方插件");
    
    private final String code;
    private final String description;
    
    PluginType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static PluginType fromCode(String code) {
        for (PluginType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown plugin type code: " + code);
    }
}
