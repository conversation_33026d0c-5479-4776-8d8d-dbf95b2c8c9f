package com.baosight.rtservice.filter.core.exception;

/**
 * 过滤系统异常基类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class FilterSystemException extends RuntimeException {
    
    private final String errorCode;
    
    public FilterSystemException(String message) {
        super(message);
        this.errorCode = null;
    }
    
    public FilterSystemException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = null;
    }
    
    public FilterSystemException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public FilterSystemException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}
