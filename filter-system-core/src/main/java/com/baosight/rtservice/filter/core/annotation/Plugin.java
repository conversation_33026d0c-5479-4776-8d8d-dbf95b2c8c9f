package com.baosight.rtservice.filter.core.annotation;

import java.lang.annotation.*;

/**
 * 插件注解
 * 用于标识过滤插件的元数据信息
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Plugin {
    
    /**
     * 插件ID，全局唯一
     */
    String id();
    
    /**
     * 插件名称
     */
    String name();
    
    /**
     * 插件描述
     */
    String description() default "";
    
    /**
     * 插件版本
     */
    String version() default "1.0.0";
    
    /**
     * 插件作者
     */
    String author() default "";
    
    /**
     * 插件类型
     * builtin: 内置插件
     * custom: 自定义插件
     */
    String type() default "builtin";
    
    /**
     * 是否启用
     */
    boolean enabled() default true;
    
    /**
     * 插件优先级，数值越小优先级越高
     */
    int priority() default 100;
    
    /**
     * 插件超时时间（毫秒）
     */
    long timeout() default 5000L;
}
