server:
  port: 8080
  servlet:
    context-path: /filter-system

spring:
  application:
    name: filter-system-4j
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************
    username: filter_user
    password: filter123456
    
    # Druid连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      
      # 监控配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
      
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"

  # Redis配置
  redis:
    host: localhost
    port: 6379
    timeout: 3000
    database: 0
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/**/*.xml

# 日志配置
logging:
  level:
    com.baosight.rtservice.filter: DEBUG
    org.springframework: INFO
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/filter-system.log

# 过滤系统配置
filter:
  system:
    # 插件配置
    plugin:
      # 插件扫描包路径
      scan-packages:
        - com.baosight.rtservice.filter.plugins.builtin
      # 插件超时时间（毫秒）
      default-timeout: 5000
      # 插件缓存配置
      cache:
        enabled: true
        max-size: 1000
        expire-after-write: 300
    
    # 执行器配置
    executor:
      # 核心线程数
      core-pool-size: 10
      # 最大线程数
      max-pool-size: 50
      # 队列容量
      queue-capacity: 200
      # 线程空闲时间（秒）
      keep-alive-seconds: 60
      # 线程名前缀
      thread-name-prefix: filter-executor-
    
    # 监控配置
    monitor:
      enabled: true
      # 统计数据保留天数
      retention-days: 30
