-- 过滤系统数据库初始化脚本 (H2数据库)

-- 插件表
CREATE TABLE IF NOT EXISTS filter_plugin (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    plugin_id VARCHAR(100) NOT NULL UNIQUE COMMENT '插件ID',
    plugin_name VARCHAR(200) NOT NULL COMMENT '插件名称',
    plugin_type VARCHAR(50) NOT NULL COMMENT '插件类型：builtin/custom',
    plugin_class VARCHAR(500) COMMENT '插件类名',
    plugin_method VARCHAR(100) COMMENT '插件方法名',
    description TEXT COMMENT '插件描述',
    version VARCHAR(50) DEFAULT '1.0.0' COMMENT '插件版本',
    author VARCHAR(100) COMMENT '插件作者',
    status VARCHAR(50) DEFAULT 'ACTIVE' COMMENT '插件状态',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    priority INT DEFAULT 100 COMMENT '插件优先级',
    timeout BIGINT DEFAULT 5000 COMMENT '插件超时时间(毫秒)',
    config_template TEXT COMMENT '配置模板',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    execute_count BIGINT DEFAULT 0 COMMENT '执行次数',
    success_count BIGINT DEFAULT 0 COMMENT '成功次数',
    failure_count BIGINT DEFAULT 0 COMMENT '失败次数',
    avg_execution_time DOUBLE DEFAULT 0 COMMENT '平均执行时间'
);

-- 过滤器组表
CREATE TABLE IF NOT EXISTS filter_group (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    group_id VARCHAR(100) NOT NULL UNIQUE COMMENT '过滤器组ID',
    group_name VARCHAR(200) NOT NULL COMMENT '过滤器组名称',
    description TEXT COMMENT '描述',
    execution_mode VARCHAR(50) DEFAULT 'CHAIN' COMMENT '执行模式：CHAIN/LOGIC/PARALLEL',
    total_timeout BIGINT DEFAULT 10000 COMMENT '总超时时间(毫秒)',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 过滤规则表
CREATE TABLE IF NOT EXISTS filter_rule (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    rule_id VARCHAR(100) NOT NULL UNIQUE COMMENT '规则ID',
    rule_name VARCHAR(200) NOT NULL COMMENT '规则名称',
    plugin_id VARCHAR(100) NOT NULL COMMENT '关联的插件ID',
    group_id VARCHAR(100) NOT NULL COMMENT '所属过滤器组ID',
    description TEXT COMMENT '规则描述',
    config TEXT COMMENT '规则配置(JSON格式)',
    priority INT DEFAULT 100 COMMENT '规则优先级',
    execution_order INT DEFAULT 0 COMMENT '执行顺序',
    timeout BIGINT DEFAULT 3000 COMMENT '规则超时时间(毫秒)',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (plugin_id) REFERENCES filter_plugin(plugin_id),
    FOREIGN KEY (group_id) REFERENCES filter_group(group_id)
);

-- 执行日志表
CREATE TABLE IF NOT EXISTS filter_execution_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    request_id VARCHAR(100) NOT NULL COMMENT '请求ID',
    group_id VARCHAR(100) COMMENT '过滤器组ID',
    rule_id VARCHAR(100) COMMENT '规则ID',
    plugin_id VARCHAR(100) COMMENT '插件ID',
    user_id VARCHAR(100) COMMENT '用户ID',
    execution_result VARCHAR(50) COMMENT '执行结果：PASS/REJECT/ERROR',
    reject_reason TEXT COMMENT '拒绝原因',
    error_message TEXT COMMENT '错误信息',
    execution_time BIGINT COMMENT '执行时间(毫秒)',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_filter_plugin_type ON filter_plugin(plugin_type);
CREATE INDEX IF NOT EXISTS idx_filter_plugin_status ON filter_plugin(status);
CREATE INDEX IF NOT EXISTS idx_filter_rule_group ON filter_rule(group_id);
CREATE INDEX IF NOT EXISTS idx_filter_rule_plugin ON filter_rule(plugin_id);
CREATE INDEX IF NOT EXISTS idx_execution_log_request ON filter_execution_log(request_id);
CREATE INDEX IF NOT EXISTS idx_execution_log_time ON filter_execution_log(create_time);
