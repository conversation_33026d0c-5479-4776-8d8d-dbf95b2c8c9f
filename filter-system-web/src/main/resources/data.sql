-- 过滤系统初始数据

-- 插入内置插件
INSERT INTO filter_plugin (plugin_id, plugin_name, plugin_type, plugin_class, description, version, author, config_template) VALUES
('time_filter', '时间过滤插件', 'builtin', 'com.baosight.rtservice.filter.plugins.builtin.TimeFilterPlugin', '根据时间段和星期几进行过滤', '1.0.0', 'RtService Team', 
'{"startTime": "09:00", "endTime": "18:00", "weekdays": [1, 2, 3, 4, 5]}');

-- 插入默认过滤器组
INSERT INTO filter_group (group_id, group_name, description, execution_mode) VALUES
('default_group', '默认过滤器组', '系统默认的过滤器组，用于演示', 'CHAIN');

-- 插入示例规则
INSERT INTO filter_rule (rule_id, rule_name, plugin_id, group_id, description, config, execution_order) VALUES
('time_rule_1', '工作时间过滤规则', 'time_filter', 'default_group', '只允许工作时间(周一到周五 9:00-18:00)的请求通过', 
'{"startTime": "09:00", "endTime": "18:00", "weekdays": [1, 2, 3, 4, 5]}', 1);
