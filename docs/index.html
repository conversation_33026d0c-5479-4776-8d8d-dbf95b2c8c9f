<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件式过滤系统技术文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 3px solid #3498db;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 20px;
        }
        
        .header .description {
            color: #34495e;
            font-size: 1em;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.8;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }
        
        .feature h3 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #7f8c8d;
            margin: 0;
            font-size: 0.9em;
        }
        
        .docs-section {
            margin-top: 40px;
        }
        
        .docs-section h2 {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }
        
        .docs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .doc-card {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .doc-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            border-color: #3498db;
        }
        
        .doc-card h3 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .doc-card p {
            color: #7f8c8d;
            margin-bottom: 20px;
            font-size: 0.95em;
            line-height: 1.6;
        }
        
        .doc-card a {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        
        .doc-card a:hover {
            background: #2980b9;
        }
        
        .doc-number {
            display: inline-block;
            background: #e74c3c;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            font-size: 0.9em;
            margin-right: 10px;
            vertical-align: middle;
        }
        
        .footer {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 2px solid #ecf0f1;
            text-align: center;
            color: #7f8c8d;
        }
        
        .footer p {
            margin: 10px 0;
        }
        
        .status-badge {
            display: inline-block;
            background: #27ae60;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 500;
            margin-left: 10px;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .docs-grid {
                grid-template-columns: 1fr;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔌 插件式过滤系统</h1>
            <div class="subtitle">技术文档中心 <span class="status-badge">v1.0</span></div>
            <div class="description">
                基于统一反射机制的智能勿扰模式解决方案，支持标准接口和自定义方法两种插件实现方式，
                通过反射适配器实现统一调用，提供灵活、可扩展的过滤能力。
            </div>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🔄 统一反射机制</h3>
                <p>支持标准接口和自定义方法两种实现方式，通过反射适配器统一调用</p>
            </div>
            <div class="feature">
                <h3>🔌 插件化架构</h3>
                <p>内置插件和自定义插件，插件是规则的载体，规则是插件的实例化应用</p>
            </div>
            <div class="feature">
                <h3>⏱️ 三层超时控制</h3>
                <p>插件超时 → 规则超时 → 过滤器组总超时，精确控制执行时间</p>
            </div>
            <div class="feature">
                <h3>🎛️ 多种执行方式</h3>
                <p>支持责任链和逻辑运算两种执行模式，满足不同业务场景</p>
            </div>
        </div>
        
        <div class="docs-section">
            <h2>📚 文档导航</h2>
            <div class="docs-grid">
                <div class="doc-card">
                    <h3><span class="doc-number">01</span>需求分析</h3>
                    <p>系统需求分析，包括功能性需求、非功能性需求、核心概念定义和业务场景分析。</p>
                    <a href="01-需求分析-插件式过滤系统需求分析.html">查看文档 →</a>
                </div>
                
                <div class="doc-card">
                    <h3><span class="doc-number">02</span>架构设计</h3>
                    <p>系统整体架构设计，包括核心组件、插件机制、统一加载器和反射适配器设计。</p>
                    <a href="02-架构设计-插件式过滤系统架构设计.html">查看文档 →</a>
                </div>
                
                <div class="doc-card">
                    <h3><span class="doc-number">03</span>数据库设计</h3>
                    <p>完整的数据库设计方案，包括表结构、字段说明、索引设计和数据示例。</p>
                    <a href="03-数据库设计-插件式过滤系统数据库设计.html">查看文档 →</a>
                </div>
                
                <div class="doc-card">
                    <h3><span class="doc-number">04</span>插件开发规范</h3>
                    <p>插件开发指南与集成规范，包括开发标准、接口规范和最佳实践。</p>
                    <a href="04-插件开发规范-插件开发指南与集成规范.html">查看文档 →</a>
                </div>
                
                <div class="doc-card">
                    <h3><span class="doc-number">05</span>界面设计</h3>
                    <p>配置管理界面设计，包括插件管理、规则配置和过滤器组管理界面。</p>
                    <a href="05-界面设计-配置管理界面设计.html">查看文档 →</a>
                </div>
                
                <div class="doc-card">
                    <h3><span class="doc-number">06</span>实施计划</h3>
                    <p>分阶段实施计划与技术方案，包括开发计划、里程碑和风险控制。</p>
                    <a href="06-实施计划-分阶段实施计划与技术方案.html">查看文档 →</a>
                </div>
                
                <div class="doc-card">
                    <h3><span class="doc-number">07</span>方案优化</h3>
                    <p>基于反馈的方案调整，包括性能优化、架构改进和功能增强。</p>
                    <a href="07-方案优化-基于反馈的方案调整.html">查看文档 →</a>
                </div>
                
                <div class="doc-card">
                    <h3><span class="doc-number">08</span>执行方式优化</h3>
                    <p>责任链与逻辑运算执行方式的详细设计和优化方案。</p>
                    <a href="08-执行方式优化-责任链与逻辑运算执行方式.html">查看文档 →</a>
                </div>
                
                <div class="doc-card">
                    <h3><span class="doc-number">09</span>工程设计</h3>
                    <p>Maven工程结构设计，包括模块划分、包结构、依赖管理和构建配置。</p>
                    <a href="09-工程设计-Maven工程结构设计.html">查看文档 →</a>
                </div>
                
                <div class="doc-card">
                    <h3><span class="doc-number">10</span>内置插件设计</h3>
                    <p>内置插件模块详细设计，包括插件实现、配置管理和测试策略。</p>
                    <a href="10-内置插件设计-内置插件模块详细设计.html">查看文档 →</a>
                </div>
                
                <div class="doc-card">
                    <h3><span class="doc-number">📖</span>系统概述</h3>
                    <p>系统整体介绍、核心特性说明和快速入门指南。</p>
                    <a href="README.html">查看文档 →</a>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>插件式过滤系统技术文档</strong></p>
            <p>版本: v1.0 | 最后更新: 2025年7月30日</p>
            <p>💡 提示: 点击上方文档卡片可查看详细内容</p>
        </div>
    </div>
</body>
</html>
