# 08-执行方式优化-责任链与逻辑运算执行方式

## 1. 执行方式重新设计

基于您的反馈，重新设计两种执行方式：

### 1.1 责任链方式执行
```java
/**
 * 责任链执行方式
 * 规则按顺序执行，任一规则拒绝则立即停止，返回拒绝结果
 * 所有规则都通过才返回允许结果
 */
public class ChainExecutionMode implements ExecutionMode {
    
    @Override
    public FilterResult execute(List<FilterRule> rules, FilterContext context) {
        for (FilterRule rule : rules) {
            FilterResult result = rule.execute(context);
            
            // 责任链模式：任一规则拒绝则立即返回
            if (!result.isAllowed()) {
                return FilterResult.deny(
                    String.format("规则[%s]拒绝: %s", rule.getRuleName(), result.getReason())
                );
            }
        }
        
        // 所有规则都通过
        return FilterResult.allow("所有规则检查通过");
    }
}
```

### 1.2 逻辑运算方式执行
```java
/**
 * 逻辑运算执行方式
 * 执行所有规则，根据逻辑操作符(AND/OR/NOT)计算最终结果
 */
public class LogicExecutionMode implements ExecutionMode {
    
    @Override
    public FilterResult execute(List<RuleRelation> ruleRelations, FilterContext context) {
        List<FilterResult> results = new ArrayList<>();
        
        // 执行所有规则
        for (RuleRelation relation : ruleRelations) {
            FilterRule rule = ruleManager.getRule(relation.getRuleId());
            FilterResult result = rule.execute(context);
            
            // 应用NOT操作符
            if (relation.getOperator() == LogicOperator.NOT) {
                result = result.negate();
            }
            
            results.add(result);
        }
        
        // 根据逻辑操作符计算最终结果
        return calculateLogicResult(results, ruleRelations);
    }
    
    private FilterResult calculateLogicResult(List<FilterResult> results, List<RuleRelation> relations) {
        boolean finalResult = true;
        StringBuilder reasonBuilder = new StringBuilder();
        
        for (int i = 0; i < results.size(); i++) {
            FilterResult result = results.get(i);
            RuleRelation relation = relations.get(i);
            
            if (relation.getOperator() == LogicOperator.AND) {
                finalResult = finalResult && result.isAllowed();
            } else if (relation.getOperator() == LogicOperator.OR) {
                finalResult = finalResult || result.isAllowed();
            }
            
            reasonBuilder.append(String.format("[%s:%s] ", 
                relation.getRuleName(), result.isAllowed() ? "通过" : "拒绝"));
        }
        
        return finalResult ? 
            FilterResult.allow("逻辑运算结果: " + reasonBuilder.toString()) :
            FilterResult.deny("逻辑运算结果: " + reasonBuilder.toString());
    }
}
```

## 2. 原有过滤规则分析与扩展

### 2.1 当前RN03过滤规则分析
```java
// 当前实现分析
public EiInfo messageFilter(EiInfo inInfo) throws ParseException {
    // 1. 是否开启过滤开关
    String notDisturbConfig = (String) config.get("notDisturb");
    if (!BooleanUtil.toBoolean(notDisturbConfig)) {
        return FilterResult.allow("勿扰模式未开启");
    }
    
    // 2. 时间段过滤（当前只支持当天，不支持跨天）
    String startTime = (String) config.get("startTime");
    String endTime = (String) config.get("endTime");
    if (StrUtil.isNotBlank(startTime) && StrUtil.isNotBlank(endTime)) {
        isFilter = isDateBetween(startTime, endTime);
    }
    
    // 3. 重复过滤规则（部分未实现）
    String repeatType = (String) config.get("repeatType");
    // - 每天：已实现
    // - 工作日：已实现  
    // - 周末：已实现
    // - 节假日：未实现
}
```

### 2.2 扩展后的过滤规则设计

#### 2.2.1 基础开关插件
```java
@Plugin(id = "basic_switch_filter", name = "基础开关过滤插件")
public class BasicSwitchFilterPlugin implements FilterRulePlugin {
    
    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        Boolean enabled = (Boolean) config.get("enabled");
        
        if (enabled == null || !enabled) {
            return FilterResult.deny("过滤功能已关闭");
        }
        
        return FilterResult.allow("过滤功能已开启");
    }
    
    @Override
    public ConfigSchema getConfigSchema() {
        return ConfigSchema.builder()
            .addField("enabled", FieldType.BOOLEAN, "启用过滤", "是否启用过滤功能", true, true)
            .build();
    }
}
```

#### 2.2.2 增强时间段插件（支持跨天）
```java
@Plugin(id = "enhanced_time_filter", name = "增强时间段过滤插件")
public class EnhancedTimeFilterPlugin implements FilterRulePlugin {
    
    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        String startTime = (String) config.get("startTime");
        String endTime = (String) config.get("endTime");
        Boolean allowCrossDay = (Boolean) config.getOrDefault("allowCrossDay", false);
        
        LocalTime now = LocalTime.now();
        LocalTime start = LocalTime.parse(startTime);
        LocalTime end = LocalTime.parse(endTime);
        
        boolean inTimeRange;
        
        if (allowCrossDay && start.isAfter(end)) {
            // 跨天时间段：如 22:00-06:00
            inTimeRange = now.isAfter(start) || now.isBefore(end);
        } else {
            // 普通时间段：如 09:00-18:00
            inTimeRange = now.isAfter(start) && now.isBefore(end);
        }
        
        return inTimeRange ? 
            FilterResult.allow("在允许的时间范围内") :
            FilterResult.deny(String.format("不在允许的时间范围内 [%s-%s]", startTime, endTime));
    }
    
    @Override
    public ConfigSchema getConfigSchema() {
        return ConfigSchema.builder()
            .addField("startTime", FieldType.TIME, "开始时间", "允许的开始时间", true, "09:00")
            .addField("endTime", FieldType.TIME, "结束时间", "允许的结束时间", true, "18:00")
            .addField("allowCrossDay", FieldType.BOOLEAN, "允许跨天", "是否支持跨天时间段", false, false)
            .build();
    }
}
```

#### 2.2.3 重复规则插件（完整实现）
```java
@Plugin(id = "repeat_rule_filter", name = "重复规则过滤插件")
public class RepeatRuleFilterPlugin implements FilterRulePlugin {
    
    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        String repeatType = (String) config.get("repeatType");
        
        switch (repeatType) {
            case "DAILY":
                return FilterResult.allow("每天都允许");
                
            case "WEEKDAY":
                return checkWeekday();
                
            case "WEEKEND":
                return checkWeekend();
                
            case "HOLIDAY":
                return checkHoliday(context);
                
            default:
                return FilterResult.deny("未知的重复类型: " + repeatType);
        }
    }
    
    private FilterResult checkWeekday() {
        int dayOfWeek = Calendar.getInstance().get(Calendar.DAY_OF_WEEK);
        boolean isWeekday = dayOfWeek >= Calendar.MONDAY && dayOfWeek <= Calendar.FRIDAY;
        
        return isWeekday ? 
            FilterResult.allow("工作日允许") :
            FilterResult.deny("非工作日不允许");
    }
    
    private FilterResult checkWeekend() {
        int dayOfWeek = Calendar.getInstance().get(Calendar.DAY_OF_WEEK);
        boolean isWeekend = dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY;
        
        return isWeekend ? 
            FilterResult.allow("周末允许") :
            FilterResult.deny("非周末不允许");
    }
    
    private FilterResult checkHoliday(FilterContext context) {
        // 调用节假日API或查询节假日表
        boolean isHoliday = holidayService.isHoliday(LocalDate.now());
        
        return isHoliday ? 
            FilterResult.allow("节假日允许") :
            FilterResult.deny("非节假日不允许");
    }
    
    @Override
    public ConfigSchema getConfigSchema() {
        return ConfigSchema.builder()
            .addField("repeatType", FieldType.ENUM, "重复类型", "选择重复规则类型", true, "DAILY")
            .addFieldOption("repeatType", "DAILY", "每天")
            .addFieldOption("repeatType", "WEEKDAY", "工作日")
            .addFieldOption("repeatType", "WEEKEND", "周末")
            .addFieldOption("repeatType", "HOLIDAY", "节假日")
            .build();
    }
}
```

## 3. 数据库表结构调整

### 3.1 过滤器组表增加执行方式字段
```sql
ALTER TABLE rs_filter_group 
ADD COLUMN execution_type VARCHAR(20) DEFAULT 'CHAIN' COMMENT '执行方式:CHAIN-责任链,LOGIC-逻辑运算';
```

### 3.2 规则关联表调整
```sql
-- 责任链方式：只需要执行顺序
-- 逻辑运算方式：需要逻辑操作符
ALTER TABLE rs_filter_rule_relation 
MODIFY COLUMN logic_operator VARCHAR(10) DEFAULT 'AND' COMMENT '逻辑操作符:AND/OR/NOT(仅逻辑运算方式使用)';
```

## 4. 配置界面调整

### 4.1 执行方式选择
```vue
<el-form-item label="执行方式">
  <el-radio-group v-model="configForm.executionType" @change="onExecutionTypeChange">
    <el-radio label="CHAIN">
      <div>
        <strong>责任链方式</strong>
        <div class="radio-desc">按顺序执行规则，任一规则拒绝则立即停止</div>
      </div>
    </el-radio>
    <el-radio label="LOGIC">
      <div>
        <strong>逻辑运算方式</strong>
        <div class="radio-desc">执行所有规则，根据逻辑操作符计算最终结果</div>
      </div>
    </el-radio>
  </el-radio-group>
</el-form-item>
```

### 4.2 规则配置表格（根据执行方式动态显示）
```vue
<el-table :data="ruleRelations" border>
  <el-table-column prop="ruleName" label="规则名称" width="200" />
  
  <!-- 责任链方式：显示执行顺序 -->
  <el-table-column 
    v-if="configForm.executionType === 'CHAIN'" 
    prop="executionOrder" 
    label="执行顺序" 
    width="120">
    <template #default="{ row }">
      <el-input-number v-model="row.executionOrder" :min="1" size="small" />
    </template>
  </el-table-column>
  
  <!-- 逻辑运算方式：显示逻辑操作符 -->
  <el-table-column 
    v-if="configForm.executionType === 'LOGIC'" 
    prop="operator" 
    label="逻辑操作符" 
    width="120">
    <template #default="{ row }">
      <el-select v-model="row.operator" size="small">
        <el-option label="AND（与）" value="AND" />
        <el-option label="OR（或）" value="OR" />
        <el-option label="NOT（非）" value="NOT" />
      </el-select>
    </template>
  </el-table-column>
  
  <el-table-column prop="timeout" label="超时时间(ms)" width="140" />
  <el-table-column label="操作" width="150" />
</el-table>
```

## 5. 现有功能迁移方案

### 5.1 迁移映射关系
```sql
-- 将现有rs_rn_config迁移为新的插件规则组合
INSERT INTO rs_filter_group (group_id, group_name, message_code, dialog_type, execution_type)
SELECT 
    CONCAT('legacy_', message_code, '_', dialog_type),
    CONCAT('迁移-', message_code),
    message_code,
    dialog_type,
    'CHAIN'  -- 使用责任链方式保持原有逻辑
FROM rs_rn_config;

-- 创建基础开关规则
INSERT INTO rs_filter_rule (rule_id, rule_name, plugin_id, rule_config)
SELECT 
    CONCAT('switch_', message_code),
    '基础开关规则',
    'basic_switch_filter',
    JSON_OBJECT('enabled', CASE WHEN not_disturb = 'true' THEN true ELSE false END)
FROM rs_rn_config;

-- 创建时间段规则（如果配置了时间）
INSERT INTO rs_filter_rule (rule_id, rule_name, plugin_id, rule_config)
SELECT 
    CONCAT('time_', message_code),
    '时间段规则',
    'enhanced_time_filter',
    JSON_OBJECT(
        'startTime', start_time,
        'endTime', end_time,
        'allowCrossDay', false
    )
FROM rs_rn_config 
WHERE start_time IS NOT NULL AND end_time IS NOT NULL;

-- 创建重复规则（如果配置了重复类型）
INSERT INTO rs_filter_rule (rule_id, rule_name, plugin_id, rule_config)
SELECT 
    CONCAT('repeat_', message_code),
    '重复规则',
    'repeat_rule_filter',
    JSON_OBJECT('repeatType', repeat_type)
FROM rs_rn_config 
WHERE repeat_type IS NOT NULL;
```

### 5.2 规则关联关系
```sql
-- 按责任链方式关联规则（顺序：开关 -> 时间 -> 重复）
-- 1. 基础开关规则（第一优先级）
INSERT INTO rs_filter_rule_relation (relation_id, group_id, rule_id, execution_order)
SELECT 
    UUID(),
    CONCAT('legacy_', message_code, '_', dialog_type),
    CONCAT('switch_', message_code),
    1
FROM rs_rn_config;

-- 2. 时间段规则（第二优先级）
INSERT INTO rs_filter_rule_relation (relation_id, group_id, rule_id, execution_order)
SELECT 
    UUID(),
    CONCAT('legacy_', message_code, '_', dialog_type),
    CONCAT('time_', message_code),
    2
FROM rs_rn_config 
WHERE start_time IS NOT NULL AND end_time IS NOT NULL;

-- 3. 重复规则（第三优先级）
INSERT INTO rs_filter_rule_relation (relation_id, group_id, rule_id, execution_order)
SELECT 
    UUID(),
    CONCAT('legacy_', message_code, '_', dialog_type),
    CONCAT('repeat_', message_code),
    3
FROM rs_rn_config 
WHERE repeat_type IS NOT NULL;
```

## 6. 执行逻辑示例

### 6.1 责任链执行示例
```
消息: SYSTEM_ALERT, 用户: admin, 时间: 2024-01-15 14:30

执行顺序：
1. 基础开关规则 -> 检查enabled=true -> 通过 -> 继续
2. 时间段规则 -> 检查14:30在09:00-18:00内 -> 通过 -> 继续  
3. 重复规则 -> 检查周一是工作日 -> 通过 -> 继续

最终结果：允许（所有规则都通过）
```

### 6.2 逻辑运算执行示例
```
消息: SYSTEM_ALERT, 用户: admin, 时间: 2024-01-15 14:30

规则配置：
1. 基础开关规则 AND enabled=true -> true
2. 时间段规则 OR 14:30在09:00-18:00内 -> true
3. 用户权限规则 AND admin有权限 -> true

逻辑运算：true AND (true OR true) AND true = true
最终结果：允许
```

## 7. 总结

### 7.1 执行方式对比

| 特性 | 责任链方式 | 逻辑运算方式 |
|------|------------|--------------|
| 执行策略 | 顺序执行，遇拒绝即停 | 全部执行，逻辑计算 |
| 性能 | 更高（可能提前结束） | 较低（必须全部执行） |
| 适用场景 | 简单过滤，快速决策 | 复杂逻辑，综合判断 |
| 配置复杂度 | 简单（只需顺序） | 复杂（需要逻辑操作符） |
| 兼容性 | 完全兼容现有逻辑 | 需要重新设计规则组合 |

### 7.2 建议
1. **现有功能迁移**：使用责任链方式，保持原有逻辑
2. **新功能开发**：根据业务需求选择合适的执行方式
3. **渐进式升级**：先实现责任链方式，后续扩展逻辑运算方式

这样的设计既保持了向后兼容，又为未来的复杂需求预留了扩展空间。
