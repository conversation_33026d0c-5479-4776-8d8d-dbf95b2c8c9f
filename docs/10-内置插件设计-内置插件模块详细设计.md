# 内置插件模块详细设计

## 📋 目录

1. [模块概述](#1-模块概述)
2. [包结构设计](#2-包结构设计)
3. [内置插件实现](#3-内置插件实现)
4. [配置管理](#4-配置管理)
5. [测试设计](#5-测试设计)

## 1. 模块概述

### 1.1 模块定位
`filter-system-plugins` 模块包含系统预置的所有内置插件，这些插件提供常用的过滤功能，开箱即用。

### 1.2 设计原则
- **标准化**: 所有内置插件必须实现FilterRulePlugin接口
- **注解驱动**: 使用@Plugin注解提供元数据
- **Spring管理**: 使用@Component注解支持依赖注入
- **配置验证**: 提供完整的配置参数验证
- **文档完整**: 每个插件都有详细的使用文档

## 2. 包结构设计

### 2.1 filter-system-plugins 包结构
```
com.company.filter.plugins/
├── builtin/                          # 内置插件实现
│   ├── BasicSwitchFilterPlugin.java  # 基础开关过滤插件
│   ├── EnhancedTimeFilterPlugin.java # 增强时间段过滤插件
│   ├── UserPermissionFilterPlugin.java # 用户权限过滤插件
│   ├── FrequencyLimitFilterPlugin.java # 频率限制过滤插件
│   └── RepeatRuleFilterPlugin.java   # 重复规则过滤插件
├── config/                           # 插件配置
│   ├── PluginAutoConfiguration.java  # 自动配置类
│   └── PluginProperties.java         # 配置属性
├── validator/                        # 配置验证器
│   ├── TimeRangeValidator.java       # 时间范围验证器
│   ├── PermissionValidator.java      # 权限验证器
│   └── FrequencyValidator.java       # 频率验证器
└── util/                            # 工具类
    ├── TimeUtils.java               # 时间工具
    ├── PermissionUtils.java         # 权限工具
    └── CacheUtils.java              # 缓存工具
```

### 2.2 模块POM配置
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.company.filter</groupId>
        <artifactId>filter-system</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>filter-system-plugins</artifactId>
    <packaging>jar</packaging>
    <name>Filter System Plugins</name>
    <description>内置插件模块</description>

    <dependencies>
        <!-- 核心模块 -->
        <dependency>
            <groupId>com.company.filter</groupId>
            <artifactId>filter-system-core</artifactId>
        </dependency>
        
        <!-- Spring Boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        
        <!-- 缓存支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        
        <!-- Redis（可选） -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <optional>true</optional>
        </dependency>
        
        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
```

## 3. 内置插件实现

### 3.1 基础开关过滤插件
```java
package com.company.filter.plugins.builtin;

import com.company.filter.core.annotation.Plugin;
import com.company.filter.core.api.FilterRulePlugin;
import com.company.filter.core.model.dto.FilterContext;
import com.company.filter.core.model.dto.FilterResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 基础开关过滤插件
 * 提供最基础的开关控制功能
 */
@Plugin(
    id = "basic_switch_filter",
    name = "基础开关过滤插件",
    version = "1.0.0",
    author = "system",
    description = "提供基础的开关控制功能，支持启用/禁用状态控制",
    timeout = 1000L
)
@Component
@Slf4j
public class BasicSwitchFilterPlugin implements FilterRulePlugin {
    
    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取开关状态
            Boolean enabled = (Boolean) config.get("enabled");
            if (enabled == null) {
                enabled = true; // 默认启用
            }
            
            log.debug("Basic switch filter executed for user: {}, enabled: {}", 
                context.getUserId(), enabled);
            
            return FilterResult.builder()
                .allowed(enabled)
                .reason(enabled ? "开关已启用" : "开关已禁用")
                .pluginId("basic_switch_filter")
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
                
        } catch (Exception e) {
            log.error("Basic switch filter execution failed", e);
            return FilterResult.builder()
                .allowed(false)
                .reason("开关过滤器执行异常: " + e.getMessage())
                .pluginId("basic_switch_filter")
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
        }
    }
    
    @Override
    public boolean validateConfig(Map<String, Object> config) {
        // 验证enabled参数
        Object enabled = config.get("enabled");
        if (enabled != null && !(enabled instanceof Boolean)) {
            log.warn("Invalid enabled parameter type: {}", enabled.getClass());
            return false;
        }
        return true;
    }
    
    @Override
    public void initialize() {
        log.info("Basic switch filter plugin initialized");
    }
    
    @Override
    public void destroy() {
        log.info("Basic switch filter plugin destroyed");
    }
}
```

### 3.2 增强时间段过滤插件
```java
package com.company.filter.plugins.builtin;

import com.company.filter.core.annotation.Plugin;
import com.company.filter.core.api.FilterRulePlugin;
import com.company.filter.core.model.dto.FilterContext;
import com.company.filter.core.model.dto.FilterResult;
import com.company.filter.plugins.util.TimeUtils;
import com.company.filter.plugins.validator.TimeRangeValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Map;

/**
 * 增强时间段过滤插件
 * 支持跨天时间段、工作日过滤等高级功能
 */
@Plugin(
    id = "enhanced_time_filter",
    name = "增强时间段过滤插件",
    version = "1.0.0",
    author = "system",
    description = "支持跨天时间段、工作日过滤的高级时间过滤功能",
    timeout = 2000L
)
@Component
@Slf4j
public class EnhancedTimeFilterPlugin implements FilterRulePlugin {
    
    @Autowired
    private TimeRangeValidator timeRangeValidator;
    
    @Autowired
    private TimeUtils timeUtils;
    
    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取配置参数
            String startTimeStr = (String) config.get("startTime");
            String endTimeStr = (String) config.get("endTime");
            Boolean allowCrossDay = (Boolean) config.get("allowCrossDay");
            String[] weekdays = (String[]) config.get("weekdays");
            
            // 检查工作日
            if (weekdays != null && weekdays.length > 0) {
                String currentDay = LocalDate.now().getDayOfWeek().toString();
                if (!Arrays.asList(weekdays).contains(currentDay)) {
                    return FilterResult.builder()
                        .allowed(false)
                        .reason("不在允许的工作日内，当前: " + currentDay)
                        .pluginId("enhanced_time_filter")
                        .executionTime(System.currentTimeMillis() - startTime)
                        .build();
                }
            }
            
            // 检查时间段
            LocalTime now = LocalTime.now();
            LocalTime start = LocalTime.parse(startTimeStr);
            LocalTime end = LocalTime.parse(endTimeStr);
            
            boolean allowed = timeUtils.isTimeInRange(now, start, end, allowCrossDay);
            
            log.debug("Enhanced time filter executed for user: {}, time: {}, allowed: {}", 
                context.getUserId(), now, allowed);
            
            return FilterResult.builder()
                .allowed(allowed)
                .reason(allowed ? "在允许时间段内" : "不在允许时间段内")
                .pluginId("enhanced_time_filter")
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
                
        } catch (Exception e) {
            log.error("Enhanced time filter execution failed", e);
            return FilterResult.builder()
                .allowed(false)
                .reason("时间过滤器执行异常: " + e.getMessage())
                .pluginId("enhanced_time_filter")
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
        }
    }
    
    @Override
    public boolean validateConfig(Map<String, Object> config) {
        return timeRangeValidator.validate(config);
    }
    
    @Override
    public void initialize() {
        log.info("Enhanced time filter plugin initialized");
    }
    
    @Override
    public void destroy() {
        log.info("Enhanced time filter plugin destroyed");
    }
}
```

### 3.4 频率限制过滤插件
```java
package com.company.filter.plugins.builtin;

import com.company.filter.core.annotation.Plugin;
import com.company.filter.core.api.FilterRulePlugin;
import com.company.filter.core.model.dto.FilterContext;
import com.company.filter.core.model.dto.FilterResult;
import com.company.filter.plugins.util.CacheUtils;
import com.company.filter.plugins.validator.FrequencyValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 频率限制过滤插件
 * 基于时间窗口的频率限制控制
 */
@Plugin(
    id = "frequency_limit_filter",
    name = "频率限制过滤插件",
    version = "1.0.0",
    author = "system",
    description = "基于时间窗口的频率限制控制，防止过度调用",
    timeout = 2000L
)
@Component
@Slf4j
public class FrequencyLimitFilterPlugin implements FilterRulePlugin {

    @Autowired
    private FrequencyValidator frequencyValidator;

    @Autowired
    private CacheUtils cacheUtils;

    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        long startTime = System.currentTimeMillis();

        try {
            String userId = context.getUserId();
            Integer maxCount = (Integer) config.get("maxCount");
            Integer timeWindow = (Integer) config.get("timeWindow"); // 秒
            String scope = (String) config.get("scope"); // USER/GLOBAL

            String cacheKey = buildCacheKey(userId, scope, context);

            // 检查频率限制
            boolean allowed = checkFrequencyLimit(cacheKey, maxCount, timeWindow);

            if (allowed) {
                // 增加计数
                incrementCounter(cacheKey, timeWindow);
            }

            log.debug("Frequency limit filter executed for user: {}, allowed: {}",
                userId, allowed);

            return FilterResult.builder()
                .allowed(allowed)
                .reason(allowed ? "频率检查通过" : "超出频率限制")
                .pluginId("frequency_limit_filter")
                .executionTime(System.currentTimeMillis() - startTime)
                .build();

        } catch (Exception e) {
            log.error("Frequency limit filter execution failed", e);
            return FilterResult.builder()
                .allowed(false)
                .reason("频率限制过滤器执行异常: " + e.getMessage())
                .pluginId("frequency_limit_filter")
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
        }
    }

    private String buildCacheKey(String userId, String scope, FilterContext context) {
        if ("GLOBAL".equals(scope)) {
            return "freq_limit:global:" + context.getRequestType();
        } else {
            return "freq_limit:user:" + userId + ":" + context.getRequestType();
        }
    }

    private boolean checkFrequencyLimit(String cacheKey, Integer maxCount, Integer timeWindow) {
        Integer currentCount = cacheUtils.getCounter(cacheKey);
        return currentCount == null || currentCount < maxCount;
    }

    private void incrementCounter(String cacheKey, Integer timeWindow) {
        cacheUtils.incrementCounter(cacheKey, timeWindow, TimeUnit.SECONDS);
    }

    @Override
    public boolean validateConfig(Map<String, Object> config) {
        return frequencyValidator.validate(config);
    }

    @Override
    public void initialize() {
        log.info("Frequency limit filter plugin initialized");
    }

    @Override
    public void destroy() {
        log.info("Frequency limit filter plugin destroyed");
    }
}
```

### 3.5 重复规则过滤插件
```java
package com.company.filter.plugins.builtin;

import com.company.filter.core.annotation.Plugin;
import com.company.filter.core.api.FilterRulePlugin;
import com.company.filter.core.model.dto.FilterContext;
import com.company.filter.core.model.dto.FilterResult;
import com.company.filter.plugins.util.CacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 重复规则过滤插件
 * 防止重复请求的过滤控制
 */
@Plugin(
    id = "repeat_rule_filter",
    name = "重复规则过滤插件",
    version = "1.0.0",
    author = "system",
    description = "防止重复请求的过滤控制，基于请求内容去重",
    timeout = 1500L
)
@Component
@Slf4j
public class RepeatRuleFilterPlugin implements FilterRulePlugin {

    @Autowired
    private CacheUtils cacheUtils;

    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        long startTime = System.currentTimeMillis();

        try {
            String userId = context.getUserId();
            Integer dedupeWindow = (Integer) config.get("dedupeWindow"); // 去重时间窗口（秒）
            String dedupeKey = (String) config.get("dedupeKey"); // 去重键字段
            Boolean allowRepeat = (Boolean) config.get("allowRepeat");

            if (allowRepeat != null && allowRepeat) {
                // 允许重复，直接通过
                return FilterResult.builder()
                    .allowed(true)
                    .reason("允许重复请求")
                    .pluginId("repeat_rule_filter")
                    .executionTime(System.currentTimeMillis() - startTime)
                    .build();
            }

            // 构建去重缓存键
            String cacheKey = buildDedupeKey(userId, dedupeKey, context);

            // 检查是否重复
            boolean isRepeat = cacheUtils.exists(cacheKey);

            if (!isRepeat) {
                // 不重复，记录到缓存
                cacheUtils.set(cacheKey, "1", dedupeWindow, TimeUnit.SECONDS);
            }

            log.debug("Repeat rule filter executed for user: {}, isRepeat: {}",
                userId, isRepeat);

            return FilterResult.builder()
                .allowed(!isRepeat)
                .reason(isRepeat ? "检测到重复请求" : "请求去重检查通过")
                .pluginId("repeat_rule_filter")
                .executionTime(System.currentTimeMillis() - startTime)
                .build();

        } catch (Exception e) {
            log.error("Repeat rule filter execution failed", e);
            return FilterResult.builder()
                .allowed(false)
                .reason("重复规则过滤器执行异常: " + e.getMessage())
                .pluginId("repeat_rule_filter")
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
        }
    }

    private String buildDedupeKey(String userId, String dedupeKey, FilterContext context) {
        StringBuilder keyBuilder = new StringBuilder("dedupe:");
        keyBuilder.append(userId).append(":");

        if (dedupeKey != null && !dedupeKey.isEmpty()) {
            // 使用自定义去重键
            Object keyValue = context.getRequestData().get(dedupeKey);
            keyBuilder.append(keyValue != null ? keyValue.toString() : "null");
        } else {
            // 使用默认去重键（请求类型）
            keyBuilder.append(context.getRequestType());
        }

        return keyBuilder.toString();
    }

    @Override
    public boolean validateConfig(Map<String, Object> config) {
        Object dedupeWindow = config.get("dedupeWindow");
        if (dedupeWindow != null && !(dedupeWindow instanceof Integer)) {
            return false;
        }

        Object allowRepeat = config.get("allowRepeat");
        if (allowRepeat != null && !(allowRepeat instanceof Boolean)) {
            return false;
        }

        return true;
    }

    @Override
    public void initialize() {
        log.info("Repeat rule filter plugin initialized");
    }

    @Override
    public void destroy() {
        log.info("Repeat rule filter plugin destroyed");
    }
}
```

## 4. 配置管理

### 4.1 自动配置类
```java
package com.company.filter.plugins.config;

import com.company.filter.plugins.util.CacheUtils;
import com.company.filter.plugins.util.PermissionUtils;
import com.company.filter.plugins.util.TimeUtils;
import com.company.filter.plugins.validator.FrequencyValidator;
import com.company.filter.plugins.validator.PermissionValidator;
import com.company.filter.plugins.validator.TimeRangeValidator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 插件自动配置类
 */
@Configuration
@EnableCaching
@EnableConfigurationProperties(PluginProperties.class)
public class PluginAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public TimeUtils timeUtils() {
        return new TimeUtils();
    }

    @Bean
    @ConditionalOnMissingBean
    public PermissionUtils permissionUtils() {
        return new PermissionUtils();
    }

    @Bean
    @ConditionalOnMissingBean
    public CacheUtils cacheUtils() {
        return new CacheUtils();
    }

    @Bean
    @ConditionalOnMissingBean
    public TimeRangeValidator timeRangeValidator() {
        return new TimeRangeValidator();
    }

    @Bean
    @ConditionalOnMissingBean
    public PermissionValidator permissionValidator() {
        return new PermissionValidator();
    }

    @Bean
    @ConditionalOnMissingBean
    public FrequencyValidator frequencyValidator() {
        return new FrequencyValidator();
    }
}
```

### 4.2 配置属性类
```java
package com.company.filter.plugins.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 插件配置属性
 */
@Data
@ConfigurationProperties(prefix = "filter.plugins")
public class PluginProperties {

    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    /**
     * 权限配置
     */
    private Permission permission = new Permission();

    /**
     * 频率限制配置
     */
    private Frequency frequency = new Frequency();

    @Data
    public static class Cache {
        /**
         * 缓存类型：memory, redis
         */
        private String type = "memory";

        /**
         * 默认过期时间（秒）
         */
        private int defaultExpire = 300;

        /**
         * 最大缓存大小
         */
        private int maxSize = 10000;
    }

    @Data
    public static class Permission {
        /**
         * 权限检查模式：local, remote
         */
        private String checkMode = "local";

        /**
         * 远程权限服务URL
         */
        private String remoteUrl;

        /**
         * 缓存权限结果时间（秒）
         */
        private int cacheTime = 300;
    }

    @Data
    public static class Frequency {
        /**
         * 默认最大频率
         */
        private int defaultMaxCount = 100;

        /**
         * 默认时间窗口（秒）
         */
        private int defaultTimeWindow = 60;

        /**
         * 是否启用全局频率限制
         */
        private boolean enableGlobal = true;
    }
}
```

## 5. 测试设计

### 5.1 单元测试示例
```java
package com.company.filter.plugins.builtin;

import com.company.filter.core.model.dto.FilterContext;
import com.company.filter.core.model.dto.FilterResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 基础开关过滤插件单元测试
 */
@ExtendWith(MockitoExtension.class)
class BasicSwitchFilterPluginTest {

    @InjectMocks
    private BasicSwitchFilterPlugin plugin;

    private FilterContext context;
    private Map<String, Object> config;

    @BeforeEach
    void setUp() {
        context = FilterContext.builder()
            .userId("testUser")
            .requestType("TEST")
            .startTime(System.currentTimeMillis())
            .build();

        config = new HashMap<>();
    }

    @Test
    void testExecute_EnabledTrue_ShouldAllow() {
        // Given
        config.put("enabled", true);

        // When
        FilterResult result = plugin.execute(context, config);

        // Then
        assertTrue(result.isAllowed());
        assertEquals("开关已启用", result.getReason());
        assertEquals("basic_switch_filter", result.getPluginId());
        assertTrue(result.getExecutionTime() >= 0);
    }

    @Test
    void testExecute_EnabledFalse_ShouldDeny() {
        // Given
        config.put("enabled", false);

        // When
        FilterResult result = plugin.execute(context, config);

        // Then
        assertFalse(result.isAllowed());
        assertEquals("开关已禁用", result.getReason());
        assertEquals("basic_switch_filter", result.getPluginId());
    }

    @Test
    void testExecute_NoConfig_ShouldDefaultToTrue() {
        // When
        FilterResult result = plugin.execute(context, config);

        // Then
        assertTrue(result.isAllowed());
        assertEquals("开关已启用", result.getReason());
    }

    @Test
    void testValidateConfig_ValidBoolean_ShouldReturnTrue() {
        // Given
        config.put("enabled", true);

        // When
        boolean valid = plugin.validateConfig(config);

        // Then
        assertTrue(valid);
    }

    @Test
    void testValidateConfig_InvalidType_ShouldReturnFalse() {
        // Given
        config.put("enabled", "invalid");

        // When
        boolean valid = plugin.validateConfig(config);

        // Then
        assertFalse(valid);
    }
}
```

### 5.2 集成测试示例
```java
package com.company.filter.plugins.builtin;

import com.company.filter.core.model.dto.FilterContext;
import com.company.filter.core.model.dto.FilterResult;
import com.company.filter.plugins.config.PluginAutoConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 增强时间段过滤插件集成测试
 */
@SpringBootTest(classes = {
    PluginAutoConfiguration.class,
    EnhancedTimeFilterPlugin.class
})
@TestPropertySource(properties = {
    "filter.plugins.cache.type=memory",
    "filter.plugins.cache.defaultExpire=300"
})
class EnhancedTimeFilterPluginIntegrationTest {

    @Autowired
    private EnhancedTimeFilterPlugin plugin;

    @Test
    void testExecute_WithinTimeRange_ShouldAllow() {
        // Given
        FilterContext context = FilterContext.builder()
            .userId("testUser")
            .requestType("TEST")
            .startTime(System.currentTimeMillis())
            .build();

        Map<String, Object> config = new HashMap<>();
        config.put("startTime", "09:00");
        config.put("endTime", "18:00");
        config.put("allowCrossDay", false);

        // When
        FilterResult result = plugin.execute(context, config);

        // Then
        assertNotNull(result);
        assertEquals("enhanced_time_filter", result.getPluginId());
        assertTrue(result.getExecutionTime() >= 0);
    }

    @Test
    void testExecute_CrossDayTimeRange_ShouldWork() {
        // Given
        FilterContext context = FilterContext.builder()
            .userId("testUser")
            .requestType("TEST")
            .startTime(System.currentTimeMillis())
            .build();

        Map<String, Object> config = new HashMap<>();
        config.put("startTime", "22:00");
        config.put("endTime", "06:00");
        config.put("allowCrossDay", true);

        // When
        FilterResult result = plugin.execute(context, config);

        // Then
        assertNotNull(result);
        assertEquals("enhanced_time_filter", result.getPluginId());

        // 验证跨天逻辑
        LocalTime now = LocalTime.now();
        boolean expectedAllowed = now.isAfter(LocalTime.of(22, 0)) ||
                                now.isBefore(LocalTime.of(6, 0));
        assertEquals(expectedAllowed, result.isAllowed());
    }
}
```

### 5.3 性能测试示例
```java
package com.company.filter.plugins.builtin;

import com.company.filter.core.model.dto.FilterContext;
import com.company.filter.core.model.dto.FilterResult;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 插件性能测试
 */
@ExtendWith(MockitoExtension.class)
class PluginPerformanceTest {

    @InjectMocks
    private BasicSwitchFilterPlugin plugin;

    @Test
    void testConcurrentExecution() throws InterruptedException {
        // Given
        int threadCount = 100;
        int executionsPerThread = 1000;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);

        FilterContext context = FilterContext.builder()
            .userId("testUser")
            .requestType("TEST")
            .startTime(System.currentTimeMillis())
            .build();

        Map<String, Object> config = new HashMap<>();
        config.put("enabled", true);

        // When
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < executionsPerThread; j++) {
                        FilterResult result = plugin.execute(context, config);
                        if (result.isAllowed()) {
                            successCount.incrementAndGet();
                        } else {
                            errorCount.incrementAndGet();
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        long endTime = System.currentTimeMillis();

        // Then
        int totalExecutions = threadCount * executionsPerThread;
        assertEquals(totalExecutions, successCount.get());
        assertEquals(0, errorCount.get());

        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / totalExecutions;

        System.out.printf("Total executions: %d%n", totalExecutions);
        System.out.printf("Total time: %d ms%n", totalTime);
        System.out.printf("Average time per execution: %.2f ms%n", avgTime);
        System.out.printf("Throughput: %.2f executions/second%n",
            totalExecutions * 1000.0 / totalTime);

        // 性能断言：平均执行时间应小于1ms
        assertTrue(avgTime < 1.0, "Average execution time should be less than 1ms");

        executor.shutdown();
    }
}
```

### 5.4 测试配置
```yaml
# test/resources/application-test.yml
filter:
  plugins:
    cache:
      type: memory
      defaultExpire: 60
      maxSize: 1000
    permission:
      checkMode: local
      cacheTime: 60
    frequency:
      defaultMaxCount: 10
      defaultTimeWindow: 60
      enableGlobal: true

logging:
  level:
    com.company.filter.plugins: DEBUG

spring:
  cache:
    type: simple
```

这个内置插件模块设计提供了完整的插件实现框架，包括标准化的插件开发模式、配置管理、工具类支持和全面的测试策略！

### 3.3 用户权限过滤插件
```java
package com.company.filter.plugins.builtin;

import com.company.filter.core.annotation.Plugin;
import com.company.filter.core.api.FilterRulePlugin;
import com.company.filter.core.model.dto.FilterContext;
import com.company.filter.core.model.dto.FilterResult;
import com.company.filter.plugins.util.PermissionUtils;
import com.company.filter.plugins.validator.PermissionValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 用户权限过滤插件
 * 基于用户角色和权限进行过滤
 */
@Plugin(
    id = "user_permission_filter",
    name = "用户权限过滤插件",
    version = "1.0.0",
    author = "system",
    description = "基于用户角色和权限进行过滤控制",
    timeout = 3000L
)
@Component
@Slf4j
public class UserPermissionFilterPlugin implements FilterRulePlugin {
    
    @Autowired
    private PermissionValidator permissionValidator;
    
    @Autowired
    private PermissionUtils permissionUtils;
    
    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        long startTime = System.currentTimeMillis();
        
        try {
            String userId = context.getUserId();
            List<String> requiredRoles = (List<String>) config.get("requiredRoles");
            List<String> requiredPermissions = (List<String>) config.get("requiredPermissions");
            String checkMode = (String) config.get("checkMode"); // AND/OR
            
            boolean hasPermission = checkUserPermission(userId, requiredRoles, requiredPermissions, checkMode);
            
            log.debug("User permission filter executed for user: {}, hasPermission: {}", 
                userId, hasPermission);
            
            return FilterResult.builder()
                .allowed(hasPermission)
                .reason(hasPermission ? "用户权限验证通过" : "用户权限不足")
                .pluginId("user_permission_filter")
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
                
        } catch (Exception e) {
            log.error("User permission filter execution failed", e);
            return FilterResult.builder()
                .allowed(false)
                .reason("权限过滤器执行异常: " + e.getMessage())
                .pluginId("user_permission_filter")
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
        }
    }
    
    @Cacheable(value = "userPermissions", key = "#userId")
    private boolean checkUserPermission(String userId, List<String> requiredRoles, 
                                      List<String> requiredPermissions, String checkMode) {
        return permissionUtils.checkPermission(userId, requiredRoles, requiredPermissions, checkMode);
    }
    
    @Override
    public boolean validateConfig(Map<String, Object> config) {
        return permissionValidator.validate(config);
    }
    
    @Override
    public void initialize() {
        log.info("User permission filter plugin initialized");
    }
    
    @Override
    public void destroy() {
        log.info("User permission filter plugin destroyed");
    }
}
```
