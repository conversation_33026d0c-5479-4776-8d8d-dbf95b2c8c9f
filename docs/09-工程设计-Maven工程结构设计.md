# Maven工程结构设计

## 📋 目录

1. [工程概述](#1-工程概述)
2. [Maven模块结构](#2-maven模块结构)
3. [核心模块包结构](#3-核心模块包结构)
4. [自定义插件SDK](#4-自定义插件sdk)
5. [Maven依赖管理](#5-maven依赖管理)
6. [构建配置](#6-构建配置)

## 1. 工程概述

### 1.1 项目结构
```
filter-system/
├── pom.xml                           # 父POM
├── filter-system-core/               # 核心模块
├── filter-system-web/                # Web管理界面
├── filter-system-sdk/                # 自定义插件SDK
├── filter-system-plugins/            # 内置插件模块
├── filter-system-examples/           # 示例项目
└── docs/                             # 文档目录
```

### 1.2 模块依赖关系
```
filter-system-web
    ├── filter-system-core
    └── filter-system-plugins

filter-system-plugins
    └── filter-system-core

filter-system-sdk
    └── filter-system-core (provided)

filter-system-examples
    └── filter-system-sdk
```

## 2. Maven模块结构

### 2.1 父POM (filter-system/pom.xml)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.company.filter</groupId>
    <artifactId>filter-system</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>Filter System</name>
    <description>插件式过滤系统</description>

    <modules>
        <module>filter-system-core</module>
        <module>filter-system-plugins</module>
        <module>filter-system-sdk</module>
        <module>filter-system-web</module>
        <module>filter-system-examples</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- 版本管理 -->
        <spring.boot.version>2.7.18</spring.boot.version>
        <mybatis.plus.version>*******</mybatis.plus.version>
        <lombok.version>1.18.28</lombok.version>
        <jackson.version>2.15.2</jackson.version>
        <slf4j.version>1.7.36</slf4j.version>
        <junit.version>5.9.3</junit.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- 内部模块 -->
            <dependency>
                <groupId>com.company.filter</groupId>
                <artifactId>filter-system-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.company.filter</groupId>
                <artifactId>filter-system-plugins</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.company.filter</groupId>
                <artifactId>filter-system-sdk</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
```

### 2.2 核心模块 (filter-system-core/pom.xml)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.company.filter</groupId>
        <artifactId>filter-system</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>filter-system-core</artifactId>
    <packaging>jar</packaging>
    <name>Filter System Core</name>
    <description>过滤系统核心模块</description>

    <dependencies>
        <!-- Spring Framework -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        
        <!-- MyBatis Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis.plus.version}</version>
        </dependency>
        
        <!-- Jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <!-- 日志 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        
        <!-- 测试 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
```

## 3. 核心模块包结构

### 3.1 filter-system-core 包结构
```
com.company.filter.core/
├── annotation/                        # 注解定义
│   ├── Plugin.java                   # 插件注解
│   └── PluginMethod.java             # 插件方法注解
├── api/                              # 核心接口
│   ├── FilterRulePlugin.java         # 插件接口
│   ├── PluginLifecycle.java          # 生命周期接口
│   └── ConfigValidator.java          # 配置验证接口
├── model/                            # 数据模型
│   ├── dto/                          # 数据传输对象
│   │   ├── FilterContext.java        # 过滤上下文
│   │   ├── FilterResult.java         # 过滤结果
│   │   ├── PluginMetadata.java       # 插件元数据
│   │   └── PluginUploadRequest.java  # 插件上传请求
│   ├── entity/                       # 数据库实体
│   │   ├── FilterPlugin.java         # 插件实体
│   │   ├── FilterRule.java           # 规则实体
│   │   └── FilterGroup.java          # 过滤器组实体
│   └── enums/                        # 枚举类
│       ├── PluginType.java           # 插件类型
│       ├── PluginStatus.java         # 插件状态
│       └── ExecutionMode.java        # 执行模式
├── engine/                           # 核心引擎
│   ├── FilterEngine.java             # 过滤引擎
│   ├── RuleOrchestrator.java         # 规则编排器
│   └── ExecutionContext.java         # 执行上下文
├── plugin/                           # 插件管理
│   ├── discovery/                    # 插件发现
│   │   ├── PluginDiscovery.java      # 插件发现接口
│   │   ├── BuiltinPluginDiscovery.java # 内置插件发现
│   │   └── CustomPluginDiscovery.java  # 自定义插件发现
│   ├── loader/                       # 插件加载
│   │   ├── UnifiedPluginLoader.java  # 统一插件加载器
│   │   ├── PluginClassLoaderManager.java # 类加载器管理
│   │   └── ReflectionPluginAdapter.java  # 反射适配器
│   ├── registry/                     # 插件注册
│   │   ├── PluginRegistry.java       # 插件注册表
│   │   └── PluginWrapper.java        # 插件包装器
│   └── lifecycle/                    # 生命周期管理
│       ├── PluginLifecycleManager.java # 生命周期管理器
│       └── PluginInitializer.java    # 插件初始化器
├── repository/                       # 数据访问层
│   ├── PluginRepository.java         # 插件仓库
│   ├── RuleRepository.java           # 规则仓库
│   └── FilterGroupRepository.java    # 过滤器组仓库
├── service/                          # 业务服务层
│   ├── PluginService.java            # 插件服务
│   ├── RuleService.java              # 规则服务
│   └── FilterService.java            # 过滤服务
├── config/                           # 配置类
│   ├── FilterSystemConfig.java       # 系统配置
│   └── PluginConfig.java             # 插件配置
├── exception/                        # 异常定义
│   ├── FilterSystemException.java    # 系统异常基类
│   ├── PluginException.java          # 插件异常
│   └── RuleException.java            # 规则异常
└── util/                            # 工具类
    ├── ReflectionUtils.java          # 反射工具
    ├── JsonUtils.java                # JSON工具
    └── ClassLoaderUtils.java         # 类加载器工具
```

### 3.2 核心类设计

#### 3.2.1 插件接口 (FilterRulePlugin.java)
```java
package com.company.filter.core.api;

import com.company.filter.core.model.dto.FilterContext;
import com.company.filter.core.model.dto.FilterResult;
import com.company.filter.core.model.dto.PluginMetadata;

import java.util.Map;

/**
 * 过滤规则插件接口
 * 所有插件都应该实现此接口
 */
public interface FilterRulePlugin {
    
    /**
     * 执行过滤逻辑
     * @param context 过滤上下文
     * @param config 插件配置参数
     * @return 过滤结果
     */
    FilterResult execute(FilterContext context, Map<String, Object> config);
    
    /**
     * 获取插件元数据
     * @return 插件元数据
     */
    default PluginMetadata getMetadata() {
        Plugin annotation = this.getClass().getAnnotation(Plugin.class);
        if (annotation != null) {
            return PluginMetadata.fromAnnotation(annotation);
        }
        return null;
    }
    
    /**
     * 插件初始化
     */
    default void initialize() {
        // 默认空实现
    }
    
    /**
     * 插件销毁
     */
    default void destroy() {
        // 默认空实现
    }
    
    /**
     * 验证配置参数
     * @param config 配置参数
     * @return 验证结果
     */
    default boolean validateConfig(Map<String, Object> config) {
        return true;
    }
}
```

#### 3.2.2 插件注解 (Plugin.java)
```java
package com.company.filter.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 插件注解
 * 用于标识插件类并提供元数据
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface Plugin {
    
    /**
     * 插件ID，全局唯一
     */
    String id();
    
    /**
     * 插件名称
     */
    String name();
    
    /**
     * 插件版本
     */
    String version() default "1.0.0";
    
    /**
     * 插件作者
     */
    String author() default "";
    
    /**
     * 插件描述
     */
    String description() default "";
    
    /**
     * 加载顺序
     */
    int order() default 0;
    
    /**
     * 默认超时时间（毫秒）
     */
    long timeout() default 5000L;
}
```

#### 3.2.3 统一插件加载器 (UnifiedPluginLoader.java)
```java
package com.company.filter.core.plugin.loader;

import com.company.filter.core.api.FilterRulePlugin;
import com.company.filter.core.model.dto.PluginMetadata;
import com.company.filter.core.model.enums.PluginType;
import com.company.filter.core.exception.PluginLoadException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 统一插件加载器
 * 支持标准接口和反射两种加载方式
 */
@Component
public class UnifiedPluginLoader {

    @Autowired
    private PluginClassLoaderManager classLoaderManager;

    /**
     * 加载插件
     * @param metadata 插件元数据
     * @return 插件实例
     */
    public FilterRulePlugin loadPlugin(PluginMetadata metadata) {
        try {
            if (PluginType.BUILTIN.equals(metadata.getPluginType())) {
                return loadBuiltinPlugin(metadata);
            } else {
                return loadCustomPlugin(metadata);
            }
        } catch (Exception e) {
            throw new PluginLoadException("Failed to load plugin: " + metadata.getPluginId(), e);
        }
    }

    private FilterRulePlugin loadBuiltinPlugin(PluginMetadata metadata) throws Exception {
        Class<?> pluginClass = Class.forName(metadata.getPluginClass());

        if (!FilterRulePlugin.class.isAssignableFrom(pluginClass)) {
            throw new PluginLoadException("Plugin must implement FilterRulePlugin interface");
        }

        FilterRulePlugin instance = (FilterRulePlugin) pluginClass.newInstance();
        instance.initialize();
        return instance;
    }

    private FilterRulePlugin loadCustomPlugin(PluginMetadata metadata) throws Exception {
        PluginClassLoader classLoader = classLoaderManager.createClassLoader(
            metadata.getPluginId(), metadata.getJarPath());

        Class<?> pluginClass = classLoader.loadClass(metadata.getPluginClass());
        Object instance = pluginClass.newInstance();

        if (instance instanceof FilterRulePlugin) {
            FilterRulePlugin plugin = (FilterRulePlugin) instance;
            plugin.initialize();
            return plugin;
        } else {
            return new ReflectionPluginAdapter(metadata, instance, classLoader);
        }
    }
}
```

#### 3.2.4 反射插件适配器 (ReflectionPluginAdapter.java)
```java
package com.company.filter.core.plugin.loader;

import com.company.filter.core.api.FilterRulePlugin;
import com.company.filter.core.model.dto.FilterContext;
import com.company.filter.core.model.dto.FilterResult;
import com.company.filter.core.model.dto.PluginMetadata;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * 反射插件适配器
 * 将任意类和方法适配为标准插件接口
 */
public class ReflectionPluginAdapter implements FilterRulePlugin {

    private final PluginMetadata metadata;
    private final Object pluginInstance;
    private final Method executeMethod;
    private final ClassLoader classLoader;

    public ReflectionPluginAdapter(PluginMetadata metadata, Object pluginInstance,
                                 ClassLoader classLoader) throws Exception {
        this.metadata = metadata;
        this.pluginInstance = pluginInstance;
        this.classLoader = classLoader;
        this.executeMethod = findExecuteMethod(pluginInstance.getClass(), metadata.getPluginMethod());

        tryCallInitialize();
    }

    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
        try {
            Thread.currentThread().setContextClassLoader(classLoader);

            Object result = executeMethod.invoke(pluginInstance, context, config);
            return adaptResult(result);

        } catch (Exception e) {
            return FilterResult.builder()
                .allowed(false)
                .reason("插件执行异常: " + e.getMessage())
                .pluginId(metadata.getPluginId())
                .build();
        } finally {
            Thread.currentThread().setContextClassLoader(originalClassLoader);
        }
    }

    @Override
    public PluginMetadata getMetadata() {
        return metadata;
    }

    private Method findExecuteMethod(Class<?> clazz, String methodName) throws NoSuchMethodException {
        // 尝试标准签名
        try {
            return clazz.getMethod(methodName, FilterContext.class, Map.class);
        } catch (NoSuchMethodException e) {
            // 查找同名方法
            Method[] methods = clazz.getMethods();
            for (Method method : methods) {
                if (method.getName().equals(methodName)) {
                    return method;
                }
            }
            throw new NoSuchMethodException("Method not found: " + methodName);
        }
    }

    private FilterResult adaptResult(Object result) {
        if (result instanceof FilterResult) {
            return (FilterResult) result;
        } else if (result instanceof Boolean) {
            return FilterResult.builder()
                .allowed((Boolean) result)
                .reason("插件返回: " + result)
                .pluginId(metadata.getPluginId())
                .build();
        } else if (result instanceof String) {
            return FilterResult.builder()
                .allowed(false)
                .reason((String) result)
                .pluginId(metadata.getPluginId())
                .build();
        } else {
            return FilterResult.builder()
                .allowed(true)
                .reason("插件执行成功")
                .pluginId(metadata.getPluginId())
                .build();
        }
    }

    private void tryCallInitialize() {
        try {
            Method initMethod = pluginInstance.getClass().getMethod("initialize");
            initMethod.invoke(pluginInstance);
        } catch (Exception e) {
            // 忽略初始化方法不存在的情况
        }
    }
}
```

## 4. 自定义插件SDK

### 4.1 SDK模块结构 (filter-system-sdk)
```
com.company.filter.sdk/
├── api/                              # SDK接口
│   ├── FilterRulePlugin.java         # 插件接口（从core复制）
│   └── PluginLifecycle.java          # 生命周期接口
├── annotation/                       # SDK注解
│   └── Plugin.java                   # 插件注解（从core复制）
├── model/                            # SDK模型
│   ├── FilterContext.java            # 过滤上下文（从core复制）
│   ├── FilterResult.java             # 过滤结果（从core复制）
│   └── PluginMetadata.java           # 插件元数据（从core复制）
├── util/                             # SDK工具类
│   ├── PluginUtils.java              # 插件工具
│   └── ConfigUtils.java              # 配置工具
└── example/                          # 示例代码
    ├── StandardPluginExample.java    # 标准接口示例
    └── CustomMethodExample.java      # 自定义方法示例
```

### 4.2 SDK POM配置 (filter-system-sdk/pom.xml)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.company.filter</groupId>
        <artifactId>filter-system</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>filter-system-sdk</artifactId>
    <packaging>jar</packaging>
    <name>Filter System SDK</name>
    <description>自定义插件开发SDK</description>

    <dependencies>
        <!-- 核心模块（provided scope，避免冲突） -->
        <dependency>
            <groupId>com.company.filter</groupId>
            <artifactId>filter-system-core</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 最小化依赖 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 源码打包 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Javadoc打包 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
```

### 4.3 SDK示例代码

#### 4.3.1 标准接口插件示例 (StandardPluginExample.java)
```java
package com.company.filter.sdk.example;

import com.company.filter.sdk.api.FilterRulePlugin;
import com.company.filter.sdk.annotation.Plugin;
import com.company.filter.sdk.model.FilterContext;
import com.company.filter.sdk.model.FilterResult;

import java.util.Map;

/**
 * 标准接口插件示例
 * 推荐的插件实现方式
 */
@Plugin(
    id = "example_standard_plugin",
    name = "标准接口插件示例",
    version = "1.0.0",
    author = "开发团队",
    description = "演示如何使用标准接口开发插件"
)
public class StandardPluginExample implements FilterRulePlugin {

    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        // 获取配置参数
        String threshold = (String) config.get("threshold");
        Boolean strictMode = (Boolean) config.get("strictMode");

        // 执行业务逻辑
        boolean allowed = performBusinessLogic(context, threshold, strictMode);

        // 返回结果
        return FilterResult.builder()
            .allowed(allowed)
            .reason(allowed ? "检查通过" : "检查失败")
            .pluginId("example_standard_plugin")
            .executionTime(System.currentTimeMillis() - context.getStartTime())
            .build();
    }

    @Override
    public void initialize() {
        // 插件初始化逻辑
        System.out.println("标准插件初始化完成");
    }

    @Override
    public void destroy() {
        // 插件销毁逻辑
        System.out.println("标准插件销毁完成");
    }

    @Override
    public boolean validateConfig(Map<String, Object> config) {
        // 配置验证逻辑
        return config.containsKey("threshold");
    }

    private boolean performBusinessLogic(FilterContext context, String threshold, Boolean strictMode) {
        // 具体的业务逻辑实现
        return true;
    }
}
```

#### 4.3.2 自定义方法插件示例 (CustomMethodExample.java)
```java
package com.company.filter.sdk.example;

import com.company.filter.sdk.model.FilterContext;
import com.company.filter.sdk.model.FilterResult;

import java.util.Map;

/**
 * 自定义方法插件示例
 * 兼容性实现方式，无需实现特定接口
 */
public class CustomMethodExample {

    /**
     * 自定义的过滤方法
     * 方法名可以任意，参数和返回类型灵活
     */
    public FilterResult customFilter(FilterContext context, Map<String, Object> config) {
        // 业务逻辑
        String userId = context.getUserId();
        String rule = (String) config.get("rule");

        boolean passed = checkCustomRule(userId, rule);

        return FilterResult.builder()
            .allowed(passed)
            .reason(passed ? "自定义规则通过" : "自定义规则失败")
            .pluginId("example_custom_method")
            .build();
    }

    /**
     * 返回Boolean类型的方法
     * 系统会自动适配为FilterResult
     */
    public boolean simpleCheck(FilterContext context, Map<String, Object> config) {
        return true;
    }

    /**
     * 返回String类型的方法
     * 系统会将字符串作为拒绝原因
     */
    public String validateUser(String userId) {
        if ("admin".equals(userId)) {
            return null; // null表示通过
        }
        return "用户权限不足"; // 非null表示拒绝原因
    }

    /**
     * 初始化方法（可选）
     * 如果存在此方法，系统会自动调用
     */
    public void initialize() {
        System.out.println("自定义方法插件初始化");
    }

    /**
     * 销毁方法（可选）
     * 如果存在此方法，系统会自动调用
     */
    public void destroy() {
        System.out.println("自定义方法插件销毁");
    }

    private boolean checkCustomRule(String userId, String rule) {
        // 自定义规则检查逻辑
        return true;
    }
}
```

## 5. Maven依赖管理

### 5.1 依赖版本统一管理
```xml
<!-- 在父POM中统一管理版本 -->
<properties>
    <!-- 框架版本 -->
    <spring.boot.version>2.7.18</spring.boot.version>
    <spring.version>5.3.30</spring.version>
    <mybatis.plus.version>*******</mybatis.plus.version>

    <!-- 数据库 -->
    <mysql.version>8.0.33</mysql.version>
    <druid.version>1.2.18</druid.version>

    <!-- 工具库 -->
    <lombok.version>1.18.28</lombok.version>
    <jackson.version>2.15.2</jackson.version>
    <guava.version>32.1.1-jre</guava.version>
    <commons.lang3.version>3.12.0</commons.lang3.version>

    <!-- 日志 -->
    <slf4j.version>1.7.36</slf4j.version>
    <logback.version>1.2.12</logback.version>

    <!-- 测试 -->
    <junit.version>5.9.3</junit.version>
    <mockito.version>4.11.0</mockito.version>
    <testcontainers.version>1.18.3</testcontainers.version>

    <!-- 插件版本 -->
    <maven.compiler.version>3.11.0</maven.compiler.version>
    <maven.surefire.version>3.1.2</maven.surefire.version>
    <maven.failsafe.version>3.1.2</maven.failsafe.version>
</properties>
```

### 5.2 核心依赖清单

#### 5.2.1 Spring Boot相关
```xml
<dependencies>
    <!-- Spring Boot Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
    </dependency>

    <!-- Spring Boot Web -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- Spring Boot AOP -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>

    <!-- Spring Boot Validation -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
</dependencies>
```

#### 5.2.2 数据库相关
```xml
<dependencies>
    <!-- MyBatis Plus -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis.plus.version}</version>
    </dependency>

    <!-- MySQL驱动 -->
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>${mysql.version}</version>
    </dependency>

    <!-- Druid连接池 -->
    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-starter</artifactId>
        <version>${druid.version}</version>
    </dependency>
</dependencies>
```

#### 5.2.3 工具库相关
```xml
<dependencies>
    <!-- Lombok -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
        <scope>provided</scope>
    </dependency>

    <!-- Jackson -->
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson.version}</version>
    </dependency>

    <!-- Guava -->
    <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
    </dependency>

    <!-- Apache Commons -->
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons.lang3.version}</version>
    </dependency>
</dependencies>
```

#### 5.2.4 测试相关
```xml
<dependencies>
    <!-- JUnit 5 -->
    <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>${junit.version}</version>
        <scope>test</scope>
    </dependency>

    <!-- Mockito -->
    <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockito.version}</version>
        <scope>test</scope>
    </dependency>

    <!-- Spring Boot Test -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
    </dependency>

    <!-- Testcontainers -->
    <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>${testcontainers.version}</version>
        <scope>test</scope>
    </dependency>

    <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>mysql</artifactId>
        <version>${testcontainers.version}</version>
        <scope>test</scope>
    </dependency>
</dependencies>
```

## 6. 构建配置

### 6.1 Maven插件配置
```xml
<build>
    <plugins>
        <!-- 编译插件 -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>${maven.compiler.version}</version>
            <configuration>
                <source>8</source>
                <target>8</target>
                <encoding>UTF-8</encoding>
            </configuration>
        </plugin>

        <!-- 单元测试插件 -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>${maven.surefire.version}</version>
            <configuration>
                <includes>
                    <include>**/*Test.java</include>
                    <include>**/*Tests.java</include>
                </includes>
            </configuration>
        </plugin>

        <!-- 集成测试插件 -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-failsafe-plugin</artifactId>
            <version>${maven.failsafe.version}</version>
            <configuration>
                <includes>
                    <include>**/*IT.java</include>
                    <include>**/*IntegrationTest.java</include>
                </includes>
            </configuration>
            <executions>
                <execution>
                    <goals>
                        <goal>integration-test</goal>
                        <goal>verify</goal>
                    </goals>
                </execution>
            </executions>
        </plugin>

        <!-- Spring Boot插件 -->
        <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <configuration>
                <excludes>
                    <exclude>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclude>
                </excludes>
            </configuration>
        </plugin>
    </plugins>
</build>
```

### 6.2 自定义插件开发模板

#### 6.2.1 插件项目模板 POM
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.company.plugin</groupId>
    <artifactId>my-custom-plugin</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <name>My Custom Plugin</name>
    <description>自定义过滤插件</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <filter.system.version>1.0.0-SNAPSHOT</filter.system.version>
    </properties>

    <dependencies>
        <!-- Filter System SDK -->
        <dependency>
            <groupId>com.company.filter</groupId>
            <artifactId>filter-system-sdk</artifactId>
            <version>${filter.system.version}</version>
        </dependency>

        <!-- 其他业务依赖 -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.14</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>

            <!-- 打包插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
```

### 6.3 部署和发布配置

#### 6.3.1 Maven仓库配置
```xml
<!-- 在父POM中配置分发管理 -->
<distributionManagement>
    <repository>
        <id>company-releases</id>
        <name>Company Release Repository</name>
        <url>http://nexus.company.com/repository/maven-releases/</url>
    </repository>
    <snapshotRepository>
        <id>company-snapshots</id>
        <name>Company Snapshot Repository</name>
        <url>http://nexus.company.com/repository/maven-snapshots/</url>
    </snapshotRepository>
</distributionManagement>

<repositories>
    <repository>
        <id>company-public</id>
        <name>Company Public Repository</name>
        <url>http://nexus.company.com/repository/maven-public/</url>
        <releases>
            <enabled>true</enabled>
        </releases>
        <snapshots>
            <enabled>true</enabled>
        </snapshots>
    </repository>
</repositories>
```

## 7. 开发指南

### 7.1 内置插件开发流程
1. 在`filter-system-plugins`模块中创建插件类
2. 使用`@Plugin`注解标识插件元数据
3. 实现`FilterRulePlugin`接口
4. 添加`@Component`注解支持Spring管理
5. 编写单元测试
6. 更新文档

### 7.2 自定义插件开发流程
1. 创建新的Maven项目
2. 引入`filter-system-sdk`依赖
3. 选择实现方式（标准接口或自定义方法）
4. 实现插件逻辑
5. 打包为JAR文件
6. 通过管理界面上传部署

### 7.3 插件测试策略
1. **单元测试**: 测试插件核心逻辑
2. **集成测试**: 测试插件在系统中的集成
3. **性能测试**: 测试插件执行性能
4. **兼容性测试**: 测试不同版本的兼容性

这个Maven工程结构设计提供了完整的插件式过滤系统开发框架，支持标准化开发和灵活扩展！
