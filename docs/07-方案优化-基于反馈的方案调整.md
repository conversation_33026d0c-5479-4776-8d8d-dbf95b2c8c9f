# 07-方案优化-基于反馈的方案调整

## 1. 问题澄清与方案调整

### 1.1 关于SDK和约定式开发

**问题**: 这种方式是不是基于约定，需要提供SDK业务方二开？

**回答**: 是的，需要提供SDK给业务方进行二次开发。

#### 1.1.1 SDK组件设计
```
rtservice-filter-sdk/
├── rtservice-filter-api.jar      # 核心API接口
├── rtservice-filter-annotations.jar  # 注解支持
├── rtservice-filter-utils.jar    # 工具类库
├── rtservice-filter-test.jar     # 测试框架
└── docs/                         # 开发文档
    ├── quick-start.html          # 快速开始
    ├── api-reference.html        # API参考
    └── examples/                 # 示例代码
```

#### 1.1.2 约定式开发规范
```java
// 约定1: 插件类命名和注解
@Plugin(
    id = "company_business_time_filter",  // 约定格式: {公司}_{业务}_{功能}_filter
    name = "业务时间过滤插件",
    configClass = TimeFilterConfig.class  // 约定: 必须指定配置类
)
public class BusinessTimeFilterPlugin extends AbstractFilterPlugin<TimeFilterConfig> {
    
    // 约定2: 必须实现execute方法
    @Override
    public FilterResult execute(FilterContext context, TimeFilterConfig config) {
        // 业务逻辑实现
        return FilterResult.allow("通过");
    }
}

// 约定3: 配置类必须实现Serializable
public class TimeFilterConfig implements Serializable {
    @ConfigField(label = "开始时间", required = true, type = FieldType.TIME)
    private String startTime;
    
    @ConfigField(label = "结束时间", required = true, type = FieldType.TIME)
    private String endTime;
    
    // getter/setter方法
}
```

#### 1.1.3 业务方集成步骤
```xml
<!-- 1. 引入SDK依赖 -->
<dependency>
    <groupId>com.baosight.rtservice</groupId>
    <artifactId>rtservice-filter-sdk</artifactId>
    <version>1.0.0</version>
    <scope>provided</scope>
</dependency>

<!-- 2. 开发插件 -->
<!-- 3. 打包部署 -->
<!-- 4. 在管理界面配置规则 -->
```

### 1.2 关于过滤链路的澄清

**问题**: 这个过滤是基于服务层和中间层（消息中心）的过滤，所以不涉及用户的过滤做不到？

**回答**: 完全正确！这是服务端过滤，不是用户端过滤。

#### 1.2.1 实际过滤链路
```
业务服务层 -> 过滤器系统 -> 消息中心 -> 客户端服务 -> 展示层弹窗
    ↑              ↑
  发起请求      过滤拦截点
```

#### 1.2.2 过滤时机和范围
```java
// 过滤发生在这里：Dialog.filter()方法中
public class Dialog {
    public EiInfo filter(String dialogType) {
        EiInfo nInfo = new EiInfo();
        nInfo.set(EiConstant.serviceName, "RN03");
        nInfo.set(EiConstant.methodName, "messageFilter");
        nInfo.set(RtConstant.rtMessageCode, this.getMessageCode());
        
        // 调用过滤器系统
        EiInfo onInfo = XLocalManager.call(nInfo);
        
        boolean isFilter = Convert.toBool(onInfo.get("isFilter"));
        if (isFilter) {
            // 被过滤，不发送到消息中心
            return onInfo;
        }
        
        // 通过过滤，继续发送
        return onInfo;
    }
}
```

#### 1.2.3 过滤上下文数据来源
```java
public class FilterContext {
    // 这些数据来自服务端，不是用户端
    private String messageCode;      // 消息标识（服务端定义）
    private String dialogType;       // 弹窗类型（服务端定义）
    private String userId;           // 目标用户ID（服务端获取）
    private String userRole;         // 用户角色（从用户系统查询）
    private String orgCode;          // 组织代码（从组织架构查询）
    private Map<String, Object> messageData;  // 消息内容（服务端组装）
    private Map<String, Object> systemContext; // 系统上下文（服务端环境）
    private long timestamp;          // 服务端时间戳
}
```

### 1.3 关于逻辑操作符和执行模式

**问题**: 逻辑操作符和执行模式（责任链短路执行）应该都属于规则执行的方式，逻辑操作只是规则组合的执行方式？

**回答**: 您的理解完全正确！让我重新整理这个概念。

#### 1.3.1 规则执行方式分类
```
规则执行方式
├── 规则组合方式（逻辑操作符）
│   ├── AND（与）- 所有规则都通过才允许
│   ├── OR（或）- 任一规则通过即允许
│   └── NOT（非）- 规则结果取反
└── 执行策略（执行模式）
    ├── 短路执行（SHORT_CIRCUIT）- 遇到确定结果立即停止
    └── 全部执行（ALL）- 执行所有规则后综合判断
```

#### 1.3.2 简化的配置模型
```java
public class FilterGroup {
    private String groupId;
    private String groupName;
    private String messageCode;
    private String dialogType;
    
    // 简化：只有执行策略，不需要复杂的编排配置
    private ExecutionMode executionMode;  // SHORT_CIRCUIT 或 ALL
    
    // 规则列表，通过关联表管理
    private List<RuleRelation> rules;
}

public class RuleRelation {
    private String ruleId;
    private int executionOrder;        // 执行顺序
    private LogicOperator operator;    // AND/OR/NOT
    private boolean required;          // 是否必需执行
}
```

### 1.4 关于规则权重

**问题**: 规则权重没有理解是做什么的？

**回答**: 规则权重用于**多规则综合评分**，但考虑到复杂度，建议简化或去除。

#### 1.4.1 权重的原始设计意图
```java
// 原始设计：多规则加权评分
public class WeightedFilterResult {
    private List<RuleResult> ruleResults;
    
    public boolean calculateFinalResult() {
        double totalScore = 0.0;
        double totalWeight = 0.0;
        
        for (RuleResult result : ruleResults) {
            totalScore += result.isAllowed() ? result.getWeight() : 0;
            totalWeight += result.getWeight();
        }
        
        // 加权平均分超过阈值才通过
        return (totalScore / totalWeight) > 0.6;
    }
}
```

#### 1.4.2 简化建议：去除权重概念
```java
// 简化后：只有通过/拒绝，没有权重
public class SimpleFilterResult {
    private boolean allowed;     // 是否允许
    private String reason;       // 原因
    private String ruleId;       // 规则ID
    
    // 不需要权重字段
}
```

### 1.5 关于配置界面简化

**问题**: 规则组合所有的配置页面都是表格或者表单配置，不能复杂？

**回答**: 完全同意！复杂的可视化编排不实用，改为简单的表格表单配置。

#### 1.5.1 简化的规则配置界面
```html
<!-- 过滤器组配置 - 简单表单 -->
<el-form :model="filterGroupForm">
  <el-form-item label="过滤器组名称">
    <el-input v-model="filterGroupForm.groupName" />
  </el-form-item>
  
  <el-form-item label="消息标识">
    <el-input v-model="filterGroupForm.messageCode" />
  </el-form-item>
  
  <el-form-item label="弹窗类型">
    <el-select v-model="filterGroupForm.dialogType">
      <el-option label="通知" value="NOTIFICATION" />
      <el-option label="弹窗" value="POPUP_WINDOW" />
    </el-select>
  </el-form-item>
  
  <el-form-item label="执行模式">
    <el-radio-group v-model="filterGroupForm.executionMode">
      <el-radio label="SHORT_CIRCUIT">短路执行</el-radio>
      <el-radio label="ALL">全部执行</el-radio>
    </el-radio-group>
  </el-form-item>
</el-form>

<!-- 规则关联配置 - 简单表格 -->
<el-table :data="ruleRelations">
  <el-table-column prop="ruleName" label="规则名称" />
  <el-table-column prop="executionOrder" label="执行顺序">
    <template #default="{ row }">
      <el-input-number v-model="row.executionOrder" :min="1" />
    </template>
  </el-table-column>
  <el-table-column prop="operator" label="逻辑操作">
    <template #default="{ row }">
      <el-select v-model="row.operator">
        <el-option label="AND" value="AND" />
        <el-option label="OR" value="OR" />
        <el-option label="NOT" value="NOT" />
      </el-select>
    </template>
  </el-table-column>
  <el-table-column prop="required" label="必需执行">
    <template #default="{ row }">
      <el-switch v-model="row.required" />
    </template>
  </el-table-column>
  <el-table-column label="操作">
    <template #default="{ row }">
      <el-button size="small" @click="removeRule(row)">删除</el-button>
    </template>
  </el-table-column>
</el-table>

<el-button @click="addRule">添加规则</el-button>
```

#### 1.5.2 规则配置界面
```html
<!-- 规则配置 - 动态表单 -->
<el-form :model="ruleForm">
  <el-form-item label="规则名称">
    <el-input v-model="ruleForm.ruleName" />
  </el-form-item>
  
  <el-form-item label="选择插件">
    <el-select v-model="ruleForm.pluginId" @change="loadConfigSchema">
      <el-option 
        v-for="plugin in plugins" 
        :key="plugin.id" 
        :label="plugin.name" 
        :value="plugin.id" />
    </el-select>
  </el-form-item>
  
  <!-- 根据插件动态生成配置表单 -->
  <div v-for="field in configSchema" :key="field.name">
    <el-form-item :label="field.label">
      <!-- 根据字段类型渲染不同的输入组件 -->
      <el-input v-if="field.type === 'STRING'" v-model="ruleForm.config[field.name]" />
      <el-input-number v-else-if="field.type === 'INTEGER'" v-model="ruleForm.config[field.name]" />
      <el-switch v-else-if="field.type === 'BOOLEAN'" v-model="ruleForm.config[field.name]" />
      <el-time-picker v-else-if="field.type === 'TIME'" v-model="ruleForm.config[field.name]" />
    </el-form-item>
  </div>
</el-form>
```

## 2. 优化后的整体方案

### 2.1 简化的架构
```
业务服务 -> Dialog.filter() -> FilterManager -> 插件执行 -> 结果返回
                                     ↓
                              简单的规则组合逻辑
                              (AND/OR/NOT + 执行顺序)
```

### 2.2 简化的数据模型
```sql
-- 简化后的表结构
CREATE TABLE rs_filter_group (
    group_id VARCHAR(64) PRIMARY KEY,
    group_name VARCHAR(100),
    message_code VARCHAR(50),
    dialog_type VARCHAR(20),
    execution_mode VARCHAR(20) DEFAULT 'SHORT_CIRCUIT',  -- 只有两种模式
    group_status VARCHAR(20) DEFAULT 'ENABLED'
);

CREATE TABLE rs_filter_rule_relation (
    relation_id VARCHAR(64) PRIMARY KEY,
    group_id VARCHAR(64),
    rule_id VARCHAR(64),
    execution_order INT,           -- 执行顺序
    logic_operator VARCHAR(10),    -- AND/OR/NOT
    is_required BOOLEAN DEFAULT FALSE  -- 是否必需执行
    -- 去除权重字段
);
```

### 2.3 简化的配置界面
- **插件管理**: 简单的列表 + 表单
- **规则管理**: 表单配置 + 动态字段生成
- **过滤器组管理**: 表格 + 表单，不需要复杂的可视化编排
- **监控统计**: 简单的图表和列表

## 3. 总结

基于您的反馈，优化后的方案：

1. **提供SDK**: 约定式开发，降低业务方集成成本
2. **明确过滤链路**: 服务端过滤，不涉及用户端
3. **简化执行方式**: 逻辑操作符 + 执行模式，概念清晰
4. **去除权重**: 简化为通过/拒绝，降低复杂度
5. **简化界面**: 表格表单配置，不做复杂的可视化编排

这样的方案更加实用，实施难度也大大降低。
