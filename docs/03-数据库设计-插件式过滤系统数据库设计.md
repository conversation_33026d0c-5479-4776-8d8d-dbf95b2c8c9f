# 03-数据库设计-插件式过滤系统数据库设计

## 1. 设计概述

### 1.1 设计原则
- **扩展性**: 支持插件和规则的动态扩展
- **灵活性**: 支持复杂的规则编排和配置
- **性能**: 优化查询性能和存储效率
- **一致性**: 保证数据的完整性和一致性
- **可维护性**: 清晰的表结构和关系设计

### 1.2 数据库兼容性
支持多种数据库，确保在不同环境下的兼容性：

- **MySQL**: 5.7+ / 8.0+
- **GBase 8s**: 3.0+
- **达梦数据库**: DM8+
- **Oracle**: 11g+ / 12c+ / 19c+
- **缓存**: Redis (配置缓存)
- **字符集**: UTF8MB4 (MySQL) / UTF8 (其他数据库)

### 1.3 多数据库兼容性设计原则
- **数据类型标准化**: 使用通用的数据类型
- **SQL语法兼容**: 避免数据库特定的语法
- **索引策略统一**: 使用标准的索引创建方式
- **约束条件通用**: 使用标准的约束语法

## 2. 表结构设计

### 2.1 插件管理表 (rs_filter_plugin)

#### 2.1.1 通用表结构（兼容多数据库）
```sql
-- 插件管理表
CREATE TABLE rs_filter_plugin (
    plugin_id VARCHAR(64) NOT NULL,
    plugin_name VARCHAR(100) NOT NULL,
    plugin_type VARCHAR(20) DEFAULT 'BUILTIN',
    plugin_class VARCHAR(200) NOT NULL,
    plugin_method VARCHAR(100),
    plugin_version VARCHAR(20) DEFAULT '1.0.0',
    plugin_status VARCHAR(20) DEFAULT 'ENABLED',
    plugin_config CLOB,
    plugin_description VARCHAR(500),
    plugin_author VARCHAR(100),
    plugin_jar_path VARCHAR(200),
    load_order INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user VARCHAR(50),
    update_user VARCHAR(50),
    CONSTRAINT pk_filter_plugin PRIMARY KEY (plugin_id),
    CONSTRAINT uk_plugin_class UNIQUE (plugin_class),
    CONSTRAINT ck_plugin_type CHECK (plugin_type IN ('BUILTIN', 'CUSTOM'))
);

-- 索引
CREATE INDEX idx_plugin_status ON rs_filter_plugin(plugin_status);
CREATE INDEX idx_create_time ON rs_filter_plugin(create_time);
```

#### 2.1.2 MySQL特定DDL
```sql
CREATE TABLE rs_filter_plugin (
    plugin_id VARCHAR(64) NOT NULL COMMENT '插件ID',
    plugin_name VARCHAR(100) NOT NULL COMMENT '插件名称',
    plugin_class VARCHAR(200) NOT NULL COMMENT '插件类名',
    plugin_version VARCHAR(20) DEFAULT '1.0.0' COMMENT '插件版本',
    plugin_status VARCHAR(20) DEFAULT 'ENABLED' COMMENT '插件状态:ENABLED/DISABLED/ERROR',
    plugin_config TEXT COMMENT '插件配置(JSON格式)',
    plugin_description VARCHAR(500) COMMENT '插件描述',
    plugin_author VARCHAR(100) COMMENT '插件作者',
    plugin_jar_path VARCHAR(200) COMMENT 'JAR包路径',
    load_order INT DEFAULT 0 COMMENT '加载顺序',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user VARCHAR(50) COMMENT '创建用户',
    update_user VARCHAR(50) COMMENT '更新用户',
    PRIMARY KEY (plugin_id),
    UNIQUE KEY uk_plugin_class (plugin_class),
    KEY idx_plugin_status (plugin_status),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='过滤插件管理表';
```

#### 2.1.3 Oracle特定DDL
```sql
CREATE TABLE rs_filter_plugin (
    plugin_id VARCHAR2(64) NOT NULL,
    plugin_name VARCHAR2(100) NOT NULL,
    plugin_class VARCHAR2(200) NOT NULL,
    plugin_version VARCHAR2(20) DEFAULT '1.0.0',
    plugin_status VARCHAR2(20) DEFAULT 'ENABLED',
    plugin_config CLOB,
    plugin_description VARCHAR2(500),
    plugin_author VARCHAR2(100),
    plugin_jar_path VARCHAR2(200),
    load_order NUMBER(10) DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user VARCHAR2(50),
    update_user VARCHAR2(50),
    CONSTRAINT pk_filter_plugin PRIMARY KEY (plugin_id),
    CONSTRAINT uk_plugin_class UNIQUE (plugin_class)
);

-- Oracle触发器实现update_time自动更新
CREATE OR REPLACE TRIGGER trg_plugin_update_time
    BEFORE UPDATE ON rs_filter_plugin
    FOR EACH ROW
BEGIN
    :NEW.update_time := CURRENT_TIMESTAMP;
END;
```

#### 2.1.4 达梦数据库特定DDL
```sql
CREATE TABLE rs_filter_plugin (
    plugin_id VARCHAR(64) NOT NULL,
    plugin_name VARCHAR(100) NOT NULL,
    plugin_class VARCHAR(200) NOT NULL,
    plugin_version VARCHAR(20) DEFAULT '1.0.0',
    plugin_status VARCHAR(20) DEFAULT 'ENABLED',
    plugin_config TEXT,
    plugin_description VARCHAR(500),
    plugin_author VARCHAR(100),
    plugin_jar_path VARCHAR(200),
    load_order INT DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user VARCHAR(50),
    update_user VARCHAR(50),
    CONSTRAINT pk_filter_plugin PRIMARY KEY (plugin_id),
    CONSTRAINT uk_plugin_class UNIQUE (plugin_class)
);
```

#### 2.1.5 GBase 8s特定DDL
```sql
CREATE TABLE rs_filter_plugin (
    plugin_id VARCHAR(64) NOT NULL,
    plugin_name VARCHAR(100) NOT NULL,
    plugin_class VARCHAR(200) NOT NULL,
    plugin_version VARCHAR(20) DEFAULT '1.0.0',
    plugin_status VARCHAR(20) DEFAULT 'ENABLED',
    plugin_config TEXT,
    plugin_description VARCHAR(500),
    plugin_author VARCHAR(100),
    plugin_jar_path VARCHAR(200),
    load_order INTEGER DEFAULT 0,
    create_time DATETIME YEAR TO FRACTION(3) DEFAULT CURRENT YEAR TO FRACTION(3),
    update_time DATETIME YEAR TO FRACTION(3) DEFAULT CURRENT YEAR TO FRACTION(3),
    create_user VARCHAR(50),
    update_user VARCHAR(50),
    CONSTRAINT pk_filter_plugin PRIMARY KEY (plugin_id),
    CONSTRAINT uk_plugin_class UNIQUE (plugin_class)
);
```

#### 2.1.2 示例数据
```sql
-- 内置插件示例
INSERT INTO rs_filter_plugin VALUES
('basic_switch_filter', '基础开关过滤插件', 'BUILTIN', 'com.baosight.rtservice.filter.plugins.BasicSwitchPlugin',
 'execute', '1.0.0', 'ENABLED', '{"defaultEnabled": true}', '基础开关控制插件', 'system',
 NULL, 1, NOW(), NOW(), 'admin', 'admin'),
('enhanced_time_filter', '增强时间段过滤插件', 'BUILTIN', 'com.baosight.rtservice.filter.plugins.TimeFilterPlugin',
 'execute', '1.2.0', 'ENABLED', '{"defaultTimeout": 1000}', '支持跨天时间段的过滤插件', 'system',
 NULL, 2, NOW(), NOW(), 'admin', 'admin'),
('repeat_rule_filter', '重复规则过滤插件', 'BUILTIN', 'com.baosight.rtservice.filter.plugins.RepeatRulePlugin',
 'execute', '1.1.0', 'ENABLED', '{"defaultRepeatType": "DAILY"}', '重复执行规则控制插件', 'system',
 NULL, 3, NOW(), NOW(), 'admin', 'admin');

-- 自定义插件示例
INSERT INTO rs_filter_plugin VALUES
('custom_oa_approval', 'OA审批过滤插件', 'CUSTOM', 'com.company.business.filter.OAApprovalFilter',
 'checkApprovalStatus', '1.0.0', 'ENABLED', NULL, '检查OA审批状态的自定义插件', 'business_team',
 '/plugins/oa-approval-filter.jar', 10, NOW(), NOW(), 'admin', 'admin'),
('custom_business_rule', '业务规则过滤插件', 'CUSTOM', 'com.company.business.filter.BusinessRuleFilter',
 'validateBusinessRule', '2.1.0', 'ENABLED', NULL, '复杂业务规则验证插件', 'dev_team',
 '/plugins/business-rule-filter.jar', 11, NOW(), NOW(), 'admin', 'admin');
```

#### 2.1.6 插件字段详细说明

##### 插件实现方式字段
| 字段名 | 说明 | 内置插件 | 自定义插件 |
|--------|------|----------|------------|
| plugin_class | 类全限定名 | 必须实现FilterRulePlugin接口 | 支持标准接口或任意类 |
| plugin_method | 执行方法名 | 固定为"execute" | 可指定任意方法名 |
| plugin_type | 插件类型 | BUILTIN（注解扫描发现） | CUSTOM（数据库配置） |
| plugin_jar_path | JAR文件路径 | NULL（在classpath中） | 必填（上传的JAR路径） |

##### 插件实现方式说明

**标准接口实现方式（推荐）**：
```sql
-- 示例：标准接口插件配置
INSERT INTO rs_filter_plugin VALUES (
    'custom_oa_approval',           -- plugin_id
    'OA审批过滤插件',               -- plugin_name
    'CUSTOM',                       -- plugin_type
    'com.company.filter.OaApprovalFilterPlugin',  -- plugin_class（实现FilterRulePlugin）
    'execute',                      -- plugin_method（标准方法名）
    '1.0.0',                        -- plugin_version
    'ENABLED',                      -- plugin_status
    '{}',                           -- plugin_config
    '根据OA审批状态进行过滤',        -- plugin_description
    '业务团队',                     -- plugin_author
    '/plugins/oa-approval-1.0.0.jar', -- plugin_jar_path
    10,                             -- load_order
    5000,                           -- timeout
    NOW(), NOW(), 'admin', 'admin'
);
```

**自定义方法实现方式（兼容性）**：
```sql
-- 示例：任意类方法插件配置
INSERT INTO rs_filter_plugin VALUES (
    'legacy_permission_check',      -- plugin_id
    '遗留权限检查插件',             -- plugin_name
    'CUSTOM',                       -- plugin_type
    'com.legacy.PermissionChecker', -- plugin_class（任意类）
    'checkUserPermission',          -- plugin_method（任意方法名）
    '1.0.0',                        -- plugin_version
    'ENABLED',                      -- plugin_status
    '{}',                           -- plugin_config
    '兼容遗留系统的权限检查',       -- plugin_description
    '遗留系统',                     -- plugin_author
    '/plugins/legacy-permission.jar', -- plugin_jar_path
    15,                             -- load_order
    3000,                           -- timeout
    NOW(), NOW(), 'admin', 'admin'
);
```

##### 超时机制说明
插件表中的超时字段用于控制插件执行的最大时间：
- **内置插件**: 超时时间由系统根据插件复杂度设定，无需手动配置
- **自定义插件**: 可根据业务需求配置超时时间，建议考虑外部API调用等因素
- **约束关系**: 规则超时 ≤ 插件超时 ≤ 过滤器组总超时

#### 2.1.7 插件开发和配置指南

##### 内置插件开发规范
1. **包路径**: 必须放在 `com.company.filter.builtin` 包下
2. **注解标识**: 使用 `@Plugin` 注解标识插件元数据
3. **接口实现**: 必须实现 `FilterRulePlugin` 接口
4. **Spring管理**: 使用 `@Component` 注解支持Spring容器管理
5. **自动发现**: 系统启动时通过注解扫描自动发现和注册

##### 自定义插件开发规范

**标准接口方式（推荐）**：
```java
@Plugin(
    id = "custom_business_rule",
    name = "自定义业务规则插件",
    version = "1.0.0",
    author = "开发团队"
)
public class CustomBusinessRulePlugin implements FilterRulePlugin {
    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        // 实现业务逻辑
        return FilterResult.builder()
            .allowed(true)
            .reason("业务规则检查通过")
            .pluginId("custom_business_rule")
            .build();
    }
}
```

**任意类方法方式（兼容性）**：
```java
public class LegacyBusinessChecker {
    public boolean validateBusiness(FilterContext context, Map<String, Object> params) {
        // 遗留业务逻辑
        return true;
    }

    public String checkStatus(String userId) {
        // 返回字符串，系统自动适配为FilterResult
        return "用户状态正常";
    }
}
```

##### 数据库配置对应关系

| 实现方式 | plugin_class | plugin_method | 系统处理方式 |
|----------|--------------|---------------|--------------|
| 标准接口 | 实现FilterRulePlugin的类 | "execute" | 直接调用 |
| 任意方法 | 任意类 | 任意方法名 | 反射适配器包装 |

### 2.2 过滤规则表 (rs_filter_rule)

#### 2.2.1 表结构
```sql
CREATE TABLE rs_filter_rule (
    rule_id VARCHAR(64) NOT NULL COMMENT '规则ID',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    plugin_id VARCHAR(64) NOT NULL COMMENT '插件ID',
    rule_config TEXT COMMENT '规则配置(JSON格式)',
    rule_status VARCHAR(20) DEFAULT 'ENABLED' COMMENT '规则状态:ENABLED/DISABLED/ERROR',
    rule_description VARCHAR(500) COMMENT '规则描述',
    rule_priority INT DEFAULT 0 COMMENT '规则优先级',
    timeout_ms INT DEFAULT 5000 COMMENT '超时时间(毫秒)',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user VARCHAR(50) COMMENT '创建用户',
    update_user VARCHAR(50) COMMENT '更新用户',
    PRIMARY KEY (rule_id),
    KEY idx_plugin_id (plugin_id),
    KEY idx_rule_status (rule_status),
    KEY idx_rule_priority (rule_priority),
    KEY idx_create_time (create_time),
    CONSTRAINT fk_rule_plugin FOREIGN KEY (plugin_id) REFERENCES rs_filter_plugin(plugin_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='过滤规则表';
```

#### 2.2.2 示例数据
```sql
INSERT INTO rs_filter_rule VALUES 
('rule_work_hours', '工作时间规则', 'time_filter', 
 '{"startTime": "09:00", "endTime": "18:00", "weekdays": [1,2,3,4,5]}', 
 'ENABLED', '只在工作时间允许弹窗', 1, 1000, 0, NOW(), NOW(), 'admin', 'admin'),
('rule_admin_users', '管理员用户规则', 'user_filter', 
 '{"allowedRoles": ["ADMIN", "MANAGER"], "blockedUsers": []}', 
 'ENABLED', '只允许管理员用户', 2, 500, 0, NOW(), NOW(), 'admin', 'admin');
```

#### 2.2.3 优先级与超时机制说明

##### 规则优先级 (rule_priority)
- **定义**: 规则的全局重要程度，数字越小优先级越高
- **作用范围**: 全局有效，跨过滤器组使用
- **用途**:
  - 默认排序依据：创建过滤器组时按优先级排序规则
  - 冲突解决：多个规则冲突时，优先级高的规则优先
  - 重要程度标识：便于管理员理解规则的重要性

##### 规则超时时间 (timeout_ms)
- **定义**: 单个规则执行的最大允许时间（毫秒）
- **约束关系**: 规则超时 ≤ 插件超时
- **默认值**: 5000ms，可根据具体业务调整
- **验证逻辑**: 创建规则时系统会验证超时时间不超过对应插件的超时限制

##### 实际应用示例
```sql
-- 示例：不同优先级和超时配置
INSERT INTO rs_filter_rule VALUES
('rule_basic_switch', '基础开关规则', 'basic_switch_filter',
 '{"enabled": true}', 'ENABLED', '总开关控制', 1, 500, 0, NOW(), NOW(), 'admin', 'admin'),
('rule_time_filter', '时间段规则', 'enhanced_time_filter',
 '{"startTime": "09:00", "endTime": "18:00"}', 'ENABLED', '工作时间过滤', 2, 1000, 0, NOW(), NOW(), 'admin', 'admin'),
('rule_user_permission', '用户权限规则', 'user_permission_filter',
 '{"allowedRoles": ["ADMIN", "MANAGER"]}', 'ENABLED', '权限检查', 3, 2000, 0, NOW(), NOW(), 'admin', 'admin');
```

### 2.3 过滤器组表 (rs_filter_group)

#### 2.3.1 表结构
```sql
CREATE TABLE rs_filter_group (
    group_id VARCHAR(64) NOT NULL COMMENT '过滤器组ID',
    group_name VARCHAR(100) NOT NULL COMMENT '过滤器组名称',
    message_code VARCHAR(50) COMMENT '消息标识',
    dialog_type VARCHAR(20) COMMENT '弹窗类型:NOTIFICATION/POPUP_WINDOW',
    execution_type VARCHAR(20) DEFAULT 'CHAIN' COMMENT '执行方式:CHAIN-责任链,LOGIC-逻辑运算',
    group_status VARCHAR(20) DEFAULT 'ENABLED' COMMENT '组状态:ENABLED/DISABLED',
    group_description VARCHAR(500) COMMENT '组描述',
    default_action VARCHAR(20) DEFAULT 'ALLOW' COMMENT '默认动作:ALLOW/DENY',
    timeout_ms INT DEFAULT 10000 COMMENT '总超时时间(毫秒)',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user VARCHAR(50) COMMENT '创建用户',
    update_user VARCHAR(50) COMMENT '更新用户',
    PRIMARY KEY (group_id),
    UNIQUE KEY uk_message_dialog (message_code, dialog_type),
    KEY idx_message_code (message_code),
    KEY idx_dialog_type (dialog_type),
    KEY idx_execution_type (execution_type),
    KEY idx_group_status (group_status),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='过滤器组表';
```

#### 2.3.2 示例数据
```sql
-- 责任链执行方式示例
INSERT INTO rs_filter_group VALUES
('group_system_alert', '系统告警过滤组', 'SYSTEM_ALERT', 'POPUP_WINDOW',
 'CHAIN', 'ENABLED', '系统告警弹窗过滤-责任链方式', 'DENY', 5000, NOW(), NOW(), 'admin', 'admin');

-- 逻辑运算执行方式示例
INSERT INTO rs_filter_group VALUES
('group_business_notify', '业务通知过滤组', 'BUSINESS_NOTIFY', 'NOTIFICATION',
 'LOGIC', 'ENABLED', '业务通知过滤-逻辑运算方式', 'ALLOW', 8000, NOW(), NOW(), 'admin', 'admin');
```

#### 2.3.3 总超时机制说明

##### 过滤器组总超时 (timeout_ms)
- **定义**: 整个勿扰配置执行的最大允许时间（毫秒）
- **作用**: 控制责任链或逻辑运算的总执行时间
- **默认值**: 10000ms（10秒），可根据规则复杂度调整

##### 责任链模式下的超时分配
```java
// 超时分配算法示例
public class ChainTimeoutManager {
    public void allocateTimeout(FilterGroup group) {
        int totalTimeout = group.getTimeout(); // 10000ms
        List<FilterRule> rules = group.getRules();

        for (FilterRule rule : rules) {
            // 计算剩余时间
            long remainingTime = totalTimeout - elapsedTime;

            // 实际超时 = min(规则超时, 插件超时, 剩余时间)
            int actualTimeout = Math.min(
                Math.min(rule.getTimeout(), plugin.getTimeout()),
                (int) remainingTime
            );

            executeRuleWithTimeout(rule, actualTimeout);
        }
    }
}
```

##### 超时层次关系总结
```
过滤器组总超时: 10000ms (整个勿扰配置的执行时间限制)
├── 规则1超时: min(1500ms, 2000ms, 10000ms) = 1500ms
├── 规则2超时: min(1200ms, 1800ms, 8500ms) = 1200ms
└── 规则3超时: min(1000ms, 1500ms, 7300ms) = 1000ms

约束关系: 规则超时 ≤ 插件超时 ≤ 总超时
```

### 2.4 规则关联表 (rs_filter_rule_relation)

#### 2.4.1 表结构
```sql
CREATE TABLE rs_filter_rule_relation (
    relation_id VARCHAR(64) NOT NULL COMMENT '关联ID',
    group_id VARCHAR(64) NOT NULL COMMENT '过滤器组ID',
    rule_id VARCHAR(64) NOT NULL COMMENT '规则ID',
    execution_order INT DEFAULT 0 COMMENT '执行顺序',
    logic_operator VARCHAR(10) DEFAULT 'AND' COMMENT '逻辑操作符:AND/OR/NOT',
    rule_weight DECIMAL(3,2) DEFAULT 1.00 COMMENT '规则权重',
    is_required TINYINT(1) DEFAULT 0 COMMENT '是否必需:0-否,1-是',
    relation_status VARCHAR(20) DEFAULT 'ENABLED' COMMENT '关联状态:ENABLED/DISABLED',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user VARCHAR(50) COMMENT '创建用户',
    update_user VARCHAR(50) COMMENT '更新用户',
    PRIMARY KEY (relation_id),
    UNIQUE KEY uk_group_rule (group_id, rule_id),
    KEY idx_group_id (group_id),
    KEY idx_rule_id (rule_id),
    KEY idx_execution_order (execution_order),
    KEY idx_relation_status (relation_status),
    CONSTRAINT fk_relation_group FOREIGN KEY (group_id) REFERENCES rs_filter_group(group_id) ON DELETE CASCADE,
    CONSTRAINT fk_relation_rule FOREIGN KEY (rule_id) REFERENCES rs_filter_rule(rule_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则关联表';
```

### 2.5 执行日志表 (rs_filter_execution_log)

#### 2.5.1 表结构
```sql
CREATE TABLE rs_filter_execution_log (
    log_id VARCHAR(64) NOT NULL COMMENT '日志ID',
    group_id VARCHAR(64) COMMENT '过滤器组ID',
    rule_id VARCHAR(64) COMMENT '规则ID',
    plugin_id VARCHAR(64) COMMENT '插件ID',
    message_code VARCHAR(50) COMMENT '消息标识',
    dialog_type VARCHAR(20) COMMENT '弹窗类型',
    user_id VARCHAR(50) COMMENT '用户ID',
    execution_result VARCHAR(20) COMMENT '执行结果:ALLOW/DENY/ERROR',
    execution_time_ms INT COMMENT '执行时间(毫秒)',
    filter_reason VARCHAR(500) COMMENT '过滤原因',
    error_message TEXT COMMENT '错误信息',
    request_data TEXT COMMENT '请求数据(JSON)',
    response_data TEXT COMMENT '响应数据(JSON)',
    execution_date DATE COMMENT '执行日期(分区字段)',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (log_id, execution_date),
    KEY idx_group_id (group_id),
    KEY idx_rule_id (rule_id),
    KEY idx_plugin_id (plugin_id),
    KEY idx_message_code (message_code),
    KEY idx_user_id (user_id),
    KEY idx_execution_result (execution_result),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
PARTITION BY RANGE (TO_DAYS(execution_date)) (
    PARTITION p202401 VALUES LESS THAN (TO_DAYS('2024-02-01')),
    PARTITION p202402 VALUES LESS THAN (TO_DAYS('2024-03-01')),
    PARTITION p202403 VALUES LESS THAN (TO_DAYS('2024-04-01')),
    PARTITION pmax VALUES LESS THAN MAXVALUE
) COMMENT='过滤执行日志表';
```

### 2.6 统计汇总表 (rs_filter_statistics)

#### 2.6.1 表结构
```sql
CREATE TABLE rs_filter_statistics (
    stat_id VARCHAR(64) NOT NULL COMMENT '统计ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    stat_hour TINYINT COMMENT '统计小时(0-23)',
    group_id VARCHAR(64) COMMENT '过滤器组ID',
    rule_id VARCHAR(64) COMMENT '规则ID',
    plugin_id VARCHAR(64) COMMENT '插件ID',
    total_count INT DEFAULT 0 COMMENT '总执行次数',
    allow_count INT DEFAULT 0 COMMENT '允许次数',
    deny_count INT DEFAULT 0 COMMENT '拒绝次数',
    error_count INT DEFAULT 0 COMMENT '错误次数',
    avg_execution_time DECIMAL(10,2) DEFAULT 0.00 COMMENT '平均执行时间(毫秒)',
    max_execution_time INT DEFAULT 0 COMMENT '最大执行时间(毫秒)',
    min_execution_time INT DEFAULT 0 COMMENT '最小执行时间(毫秒)',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (stat_id),
    UNIQUE KEY uk_stat_dimension (stat_date, stat_hour, group_id, rule_id, plugin_id),
    KEY idx_stat_date (stat_date),
    KEY idx_group_id (group_id),
    KEY idx_rule_id (rule_id),
    KEY idx_plugin_id (plugin_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='过滤统计表';
```

### 2.7 配置版本表 (rs_filter_config_version)

#### 2.7.1 表结构
```sql
CREATE TABLE rs_filter_config_version (
    version_id VARCHAR(64) NOT NULL COMMENT '版本ID',
    config_type VARCHAR(20) NOT NULL COMMENT '配置类型:PLUGIN/RULE/GROUP',
    config_id VARCHAR(64) NOT NULL COMMENT '配置ID',
    version_number INT NOT NULL COMMENT '版本号',
    config_content TEXT COMMENT '配置内容(JSON)',
    change_description VARCHAR(500) COMMENT '变更描述',
    is_current TINYINT(1) DEFAULT 0 COMMENT '是否当前版本:0-否,1-是',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user VARCHAR(50) COMMENT '创建用户',
    PRIMARY KEY (version_id),
    UNIQUE KEY uk_config_version (config_type, config_id, version_number),
    KEY idx_config_id (config_id),
    KEY idx_is_current (is_current),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置版本表';
```

## 3. 数据关系图

```
rs_filter_plugin (1) ──── (N) rs_filter_rule
       │                         │
       │                         │
       └─── (N) rs_filter_execution_log
                                 │
                                 │
rs_filter_group (1) ──── (N) rs_filter_rule_relation (N) ──── (1) rs_filter_rule
       │                                                              │
       │                                                              │
       └─── (N) rs_filter_execution_log ──── (N) rs_filter_statistics
       │
       └─── (N) rs_filter_config_version
```

## 4. 索引设计

### 4.1 主要查询场景
1. **根据消息标识和弹窗类型查找过滤器组**
2. **根据过滤器组查找关联的规则**
3. **根据插件ID查找规则**
4. **查询执行日志和统计信息**
5. **配置版本管理**

### 4.2 索引策略
```sql
-- 复合索引优化查询
CREATE INDEX idx_group_message_dialog ON rs_filter_group(message_code, dialog_type, group_status);
CREATE INDEX idx_relation_group_order ON rs_filter_rule_relation(group_id, execution_order, relation_status);
CREATE INDEX idx_log_date_result ON rs_filter_execution_log(execution_date, execution_result);
CREATE INDEX idx_stat_date_group ON rs_filter_statistics(stat_date, group_id);

-- 覆盖索引优化
CREATE INDEX idx_rule_plugin_status ON rs_filter_rule(plugin_id, rule_status, rule_priority);
```

## 5. 数据迁移

### 5.1 现有数据迁移
```sql
-- 迁移现有勿扰配置到新的过滤器组
INSERT INTO rs_filter_group (group_id, group_name, message_code, dialog_type, orchestration_config, group_status)
SELECT 
    CONCAT('legacy_', message_code, '_', dialog_type) as group_id,
    CONCAT('迁移-', message_code) as group_name,
    message_code,
    dialog_type,
    CASE 
        WHEN not_disturb = 'true' AND start_time IS NOT NULL AND end_time IS NOT NULL THEN
            JSON_OBJECT('mode', 'SHORT_CIRCUIT', 'expression', 
                JSON_OBJECT('operator', 'AND', 'children', 
                    JSON_ARRAY(JSON_OBJECT('ruleId', CONCAT('legacy_time_', message_code)))))
        ELSE
            JSON_OBJECT('mode', 'SHORT_CIRCUIT', 'expression', 
                JSON_OBJECT('operator', 'AND', 'children', JSON_ARRAY()))
    END as orchestration_config,
    CASE WHEN not_disturb = 'true' THEN 'ENABLED' ELSE 'DISABLED' END as group_status
FROM rs_rn_config;

-- 迁移时间规则
INSERT INTO rs_filter_rule (rule_id, rule_name, plugin_id, rule_config, rule_status)
SELECT 
    CONCAT('legacy_time_', message_code) as rule_id,
    CONCAT('时间规则-', message_code) as rule_name,
    'time_filter' as plugin_id,
    JSON_OBJECT('startTime', start_time, 'endTime', end_time) as rule_config,
    'ENABLED' as rule_status
FROM rs_rn_config 
WHERE not_disturb = 'true' AND start_time IS NOT NULL AND end_time IS NOT NULL;
```

## 6. 性能优化

### 6.1 分区策略
- **执行日志表**: 按日期分区，便于历史数据清理
- **统计表**: 按月分区，优化查询性能

### 6.2 缓存策略
```sql
-- 热点数据缓存
-- 1. 过滤器组配置 (Redis Key: filter:group:{messageCode}:{dialogType})
-- 2. 规则配置 (Redis Key: filter:rule:{ruleId})
-- 3. 插件信息 (Redis Key: filter:plugin:{pluginId})
-- 4. 执行统计 (Redis Key: filter:stats:{date}:{groupId})
```

### 6.3 数据清理策略
```sql
-- 定期清理历史日志 (保留3个月)
DELETE FROM rs_filter_execution_log 
WHERE execution_date < DATE_SUB(CURDATE(), INTERVAL 3 MONTH);

-- 定期清理历史统计 (保留1年)
DELETE FROM rs_filter_statistics 
WHERE stat_date < DATE_SUB(CURDATE(), INTERVAL 1 YEAR);
```

## 7. 数据字典

### 7.1 枚举值定义

#### 7.1.1 插件状态 (plugin_status)
- `ENABLED`: 启用
- `DISABLED`: 禁用
- `ERROR`: 错误

#### 7.1.2 规则状态 (rule_status)
- `ENABLED`: 启用
- `DISABLED`: 禁用
- `ERROR`: 错误

#### 7.1.3 执行方式 (execution_type)
- `CHAIN`: 责任链方式执行
- `LOGIC`: 逻辑运算方式执行

#### 7.1.4 逻辑操作符 (logic_operator)
- `AND`: 与操作
- `OR`: 或操作
- `NOT`: 非操作

#### 7.1.5 执行结果 (execution_result)
- `ALLOW`: 允许
- `DENY`: 拒绝
- `ERROR`: 错误

### 7.2 配置格式规范

#### 7.2.1 插件配置格式
```json
{
  "defaultTimeout": 1000,
  "cacheEnabled": true,
  "cacheTimeout": 300,
  "retryCount": 3,
  "customProperties": {
    "key1": "value1",
    "key2": "value2"
  }
}
```

#### 7.2.2 规则配置格式
```json
{
  "startTime": "09:00",
  "endTime": "18:00",
  "weekdays": [1, 2, 3, 4, 5],
  "allowedRoles": ["ADMIN", "MANAGER"],
  "blockedUsers": ["user1", "user2"],
  "threshold": 100,
  "customParams": {
    "param1": "value1"
  }
}
```

#### 7.2.3 编排配置格式
```json
{
  "mode": "SHORT_CIRCUIT",
  "timeout": 10000,
  "expression": {
    "operator": "AND",
    "children": [
      {
        "ruleId": "rule1",
        "weight": 1.0,
        "timeout": 1000
      },
      {
        "operator": "OR",
        "children": [
          {"ruleId": "rule2"},
          {"ruleId": "rule3"}
        ]
      }
    ]
  }
}
```

## 8. 数据安全

### 8.1 敏感数据加密
- 插件配置中的敏感信息
- 规则配置中的密钥信息
- 用户相关的隐私数据

### 8.2 访问控制
- 基于角色的数据访问控制
- 操作审计和日志记录
- 数据变更权限管理

### 8.3 备份策略
- 每日全量备份
- 实时增量备份
- 跨地域备份存储

## 9. 总结

本数据库设计具有以下特点：

1. **扩展性强**: 支持插件和规则的动态扩展
2. **性能优化**: 合理的索引和分区设计
3. **数据完整性**: 完善的约束和关系设计
4. **可维护性**: 清晰的表结构和命名规范
5. **监控友好**: 完整的日志和统计表设计

该设计能够满足插件式过滤系统的各种数据存储需求，同时为系统的高性能运行提供了良好的数据基础。
