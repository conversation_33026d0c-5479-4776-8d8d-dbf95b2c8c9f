# 05-界面设计-配置管理界面设计

## 1. 界面设计概述

### 1.1 设计目标
- 提供直观易用的插件管理界面
- 支持可视化的规则配置和编排
- 提供实时的监控和统计功能
- 确保操作的安全性和可追溯性

### 1.2 设计原则
- **用户友好**: 界面简洁直观，操作流程清晰
- **功能完整**: 覆盖插件管理的全生命周期
- **响应式设计**: 支持不同屏幕尺寸的设备
- **可访问性**: 符合无障碍设计标准

### 1.3 技术选型
- **前端框架**: Vue.js 3.x + Element Plus
- **状态管理**: Vuex 4.x
- **路由管理**: Vue Router 4.x
- **HTTP客户端**: Axios
- **图表组件**: ECharts
- **代码编辑器**: Monaco Editor

## 2. 整体布局设计

### 2.1 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│                        顶部导航栏                            │
├─────────────────────────────────────────────────────────────┤
│ 侧边栏  │                   主内容区                        │
│        │  ┌─────────────────────────────────────────────┐   │
│ 插件管理 │  │                                           │   │
│ 规则管理 │  │              页面内容                      │   │
│ 过滤器   │  │                                           │   │
│ 监控统计 │  │                                           │   │
│ 系统设置 │  └─────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 导航结构
```
过滤系统管理
├── 插件管理
│   ├── 插件列表
│   ├── 插件上传
│   └── 插件详情
├── 规则管理
│   ├── 规则列表
│   ├── 规则配置
│   └── 规则测试
├── 过滤器管理
│   ├── 过滤器组列表
│   ├── 规则编排
│   └── 消息绑定
├── 监控统计
│   ├── 执行统计
│   ├── 性能监控
│   └── 错误日志
└── 系统设置
    ├── 系统配置
    ├── 用户权限
    └── 操作日志
```

## 3. 插件管理界面

### 3.1 插件列表页面

#### 3.1.1 页面布局
```vue
<template>
  <div class="plugin-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>插件管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showUploadDialog">
          <el-icon><Upload /></el-icon>
          上传插件
        </el-button>
        <el-button @click="refreshPlugins">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索过滤 -->
    <div class="search-bar">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="插件名称">
          <el-input v-model="searchForm.name" placeholder="请输入插件名称" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="全部" value="" />
            <el-option label="启用" value="ENABLED" />
            <el-option label="禁用" value="DISABLED" />
            <el-option label="错误" value="ERROR" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchPlugins">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 插件列表 -->
    <el-table :data="pluginList" v-loading="loading">
      <el-table-column prop="pluginId" label="插件ID" width="200" />
      <el-table-column prop="pluginName" label="插件名称" width="200" />
      <el-table-column prop="version" label="版本" width="100" />
      <el-table-column prop="author" label="作者" width="120" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column label="操作" width="300">
        <template #default="{ row }">
          <el-button size="small" @click="viewPlugin(row)">详情</el-button>
          <el-button size="small" @click="configPlugin(row)">配置</el-button>
          <el-button 
            size="small" 
            :type="row.status === 'ENABLED' ? 'warning' : 'success'"
            @click="togglePlugin(row)">
            {{ row.status === 'ENABLED' ? '禁用' : '启用' }}
          </el-button>
          <el-button size="small" type="danger" @click="deletePlugin(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
```

#### 3.1.2 插件上传对话框
```vue
<el-dialog v-model="uploadDialogVisible" title="上传插件" width="600px">
  <el-form :model="uploadForm" :rules="uploadRules" ref="uploadFormRef">
    <el-form-item label="插件文件" prop="file" required>
      <el-upload
        class="upload-demo"
        drag
        :auto-upload="false"
        :on-change="handleFileChange"
        :file-list="fileList"
        accept=".jar">
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将JAR文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传jar文件，且不超过50MB
          </div>
        </template>
      </el-upload>
    </el-form-item>
    
    <el-form-item label="插件描述">
      <el-input
        v-model="uploadForm.description"
        type="textarea"
        :rows="3"
        placeholder="请输入插件描述"
      />
    </el-form-item>
    
    <el-form-item label="自动启用">
      <el-switch v-model="uploadForm.autoEnable" />
    </el-form-item>
  </el-form>
  
  <template #footer>
    <span class="dialog-footer">
      <el-button @click="uploadDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="uploadPlugin" :loading="uploading">
        上传
      </el-button>
    </span>
  </template>
</el-dialog>
```

### 3.2 插件详情页面

#### 3.2.1 基本信息展示
```vue
<template>
  <div class="plugin-detail">
    <el-card class="plugin-info-card">
      <template #header>
        <div class="card-header">
          <span>插件基本信息</span>
          <el-button type="primary" size="small" @click="editPlugin">
            编辑
          </el-button>
        </div>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="插件ID">
          {{ pluginInfo.pluginId }}
        </el-descriptions-item>
        <el-descriptions-item label="插件名称">
          {{ pluginInfo.pluginName }}
        </el-descriptions-item>
        <el-descriptions-item label="版本">
          {{ pluginInfo.version }}
        </el-descriptions-item>
        <el-descriptions-item label="作者">
          {{ pluginInfo.author }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(pluginInfo.status)">
            {{ getStatusText(pluginInfo.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ pluginInfo.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ pluginInfo.description }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 插件配置 -->
    <el-card class="plugin-config-card">
      <template #header>
        <span>插件配置</span>
      </template>
      
      <monaco-editor
        v-model="pluginConfig"
        language="json"
        :options="editorOptions"
        height="300px"
      />
      
      <div class="config-actions">
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
        <el-button @click="resetConfig">重置</el-button>
        <el-button @click="validateConfig">验证配置</el-button>
      </div>
    </el-card>

    <!-- 使用统计 -->
    <el-card class="plugin-stats-card">
      <template #header>
        <span>使用统计</span>
      </template>
      
      <div class="stats-container">
        <div class="stat-item">
          <div class="stat-value">{{ stats.totalRules }}</div>
          <div class="stat-label">关联规则数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ stats.totalExecutions }}</div>
          <div class="stat-label">总执行次数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ stats.avgExecutionTime }}ms</div>
          <div class="stat-label">平均执行时间</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ stats.successRate }}%</div>
          <div class="stat-label">成功率</div>
        </div>
      </div>
    </el-card>
  </div>
</template>
```

## 4. 规则管理界面

### 4.1 规则列表页面
```vue
<template>
  <div class="rule-management">
    <div class="page-header">
      <h2>规则管理</h2>
      <el-button type="primary" @click="createRule">
        <el-icon><Plus /></el-icon>
        创建规则
      </el-button>
    </div>

    <!-- 规则列表 -->
    <el-table :data="ruleList" v-loading="loading">
      <el-table-column prop="ruleName" label="规则名称" width="200" />
      <el-table-column prop="pluginName" label="所属插件" width="150" />
      <el-table-column prop="ruleStatus" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.ruleStatus)">
            {{ getStatusText(row.ruleStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="priority" label="优先级" width="100" />
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column label="操作" width="250">
        <template #default="{ row }">
          <el-button size="small" @click="editRule(row)">编辑</el-button>
          <el-button size="small" @click="testRule(row)">测试</el-button>
          <el-button size="small" @click="copyRule(row)">复制</el-button>
          <el-button size="small" type="danger" @click="deleteRule(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
```

### 4.2 规则配置页面
```vue
<template>
  <div class="rule-config">
    <el-form :model="ruleForm" :rules="ruleRules" ref="ruleFormRef" label-width="120px">
      <el-form-item label="规则名称" prop="ruleName">
        <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称" />
      </el-form-item>
      
      <el-form-item label="选择插件" prop="pluginId">
        <el-select v-model="ruleForm.pluginId" @change="onPluginChange">
          <el-option
            v-for="plugin in pluginOptions"
            :key="plugin.pluginId"
            :label="plugin.pluginName"
            :value="plugin.pluginId"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="规则描述">
        <el-input
          v-model="ruleForm.description"
          type="textarea"
          :rows="3"
          placeholder="请输入规则描述"
        />
      </el-form-item>
      
      <el-form-item label="优先级" prop="priority">
        <el-input-number v-model="ruleForm.priority" :min="0" :max="100" />
      </el-form-item>
      
      <el-form-item label="超时时间(ms)" prop="timeout">
        <el-input-number v-model="ruleForm.timeout" :min="100" :max="30000" />
      </el-form-item>
      
      <!-- 动态配置表单 -->
      <div v-if="configSchema" class="config-section">
        <h3>规则配置</h3>
        <div v-for="field in configSchema.fields" :key="field.name">
          <el-form-item :label="field.label" :prop="`config.${field.name}`">
            <!-- 字符串类型 -->
            <el-input
              v-if="field.type === 'STRING'"
              v-model="ruleForm.config[field.name]"
              :placeholder="field.description"
            />
            
            <!-- 数字类型 -->
            <el-input-number
              v-else-if="field.type === 'INTEGER'"
              v-model="ruleForm.config[field.name]"
            />
            
            <!-- 布尔类型 -->
            <el-switch
              v-else-if="field.type === 'BOOLEAN'"
              v-model="ruleForm.config[field.name]"
            />
            
            <!-- 时间类型 -->
            <el-time-picker
              v-else-if="field.type === 'TIME'"
              v-model="ruleForm.config[field.name]"
              format="HH:mm"
            />
            
            <!-- 数组类型 -->
            <el-select
              v-else-if="field.type === 'ARRAY'"
              v-model="ruleForm.config[field.name]"
              multiple
              :placeholder="field.description"
            >
              <el-option
                v-for="option in field.options"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>
      
      <el-form-item>
        <el-button type="primary" @click="saveRule">保存</el-button>
        <el-button @click="testRuleConfig">测试配置</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
```

### 4.3 规则测试页面
```vue
<template>
  <div class="rule-test">
    <el-card>
      <template #header>
        <span>规则测试</span>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="测试数据">
          <monaco-editor
            v-model="testForm.contextData"
            language="json"
            height="200px"
            :options="{ theme: 'vs-dark' }"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="runTest" :loading="testing">
            运行测试
          </el-button>
          <el-button @click="loadSampleData">加载示例数据</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 测试结果 -->
      <div v-if="testResult" class="test-result">
        <h4>测试结果</h4>
        <el-alert
          :type="testResult.allowed ? 'success' : 'warning'"
          :title="testResult.allowed ? '通过' : '拒绝'"
          :description="testResult.reason"
          show-icon
        />
        
        <el-descriptions :column="2" border class="result-details">
          <el-descriptions-item label="执行时间">
            {{ testResult.executionTime }}ms
          </el-descriptions-item>
          <el-descriptions-item label="插件ID">
            {{ testResult.pluginId }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="testResult.metadata" class="metadata">
          <h5>元数据</h5>
          <pre>{{ JSON.stringify(testResult.metadata, null, 2) }}</pre>
        </div>
      </div>
    </el-card>
  </div>
</template>
```

## 5. 过滤器管理界面

### 5.1 过滤器组列表
```vue
<template>
  <div class="filter-group-management">
    <div class="page-header">
      <h2>过滤器管理</h2>
      <el-button type="primary" @click="createFilterGroup">
        <el-icon><Plus /></el-icon>
        创建过滤器组
      </el-button>
    </div>

    <el-table :data="filterGroupList" v-loading="loading">
      <el-table-column prop="groupName" label="过滤器组名称" width="200" />
      <el-table-column prop="messageCode" label="消息标识" width="150" />
      <el-table-column prop="dialogType" label="弹窗类型" width="120" />
      <el-table-column prop="ruleCount" label="规则数量" width="100" />
      <el-table-column prop="executionMode" label="执行模式" width="120" />
      <el-table-column prop="groupStatus" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.groupStatus)">
            {{ getStatusText(row.groupStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250">
        <template #default="{ row }">
          <el-button size="small" @click="editFilterGroup(row)">编辑</el-button>
          <el-button size="small" @click="configRules(row)">规则编排</el-button>
          <el-button size="small" @click="testFilterGroup(row)">测试</el-button>
          <el-button size="small" type="danger" @click="deleteFilterGroup(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
```

### 5.2 简化的规则关联配置界面
```vue
<template>
  <div class="rule-relation-config">
    <div class="config-header">
      <h3>规则配置 - {{ filterGroup.groupName }}</h3>
      <div class="header-actions">
        <el-button @click="addRule">添加规则</el-button>
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
      </div>
    </div>

    <!-- 基础配置 -->
    <el-card class="basic-config">
      <template #header>
        <span>基础配置</span>
      </template>

      <el-form :model="configForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="消息标识">
              <el-input v-model="configForm.messageCode" placeholder="请输入消息标识" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="弹窗类型">
              <el-select v-model="configForm.dialogType" placeholder="请选择弹窗类型">
                <el-option label="通知" value="NOTIFICATION" />
                <el-option label="弹窗" value="POPUP_WINDOW" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="执行方式">
          <el-radio-group v-model="configForm.executionType" @change="onExecutionTypeChange">
            <el-radio label="CHAIN">
              <div>
                <strong>责任链方式</strong>
                <div style="font-size: 12px; color: #666; margin-top: 4px;">
                  按顺序执行规则，任一规则拒绝则立即停止，适合简单过滤场景
                </div>
              </div>
            </el-radio>
            <el-radio label="LOGIC">
              <div>
                <strong>逻辑运算方式</strong>
                <div style="font-size: 12px; color: #666; margin-top: 4px;">
                  执行所有规则，根据逻辑操作符计算最终结果，适合复杂逻辑场景
                </div>
              </div>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="默认动作">
          <el-radio-group v-model="configForm.defaultAction">
            <el-radio label="ALLOW">允许（无规则时默认允许）</el-radio>
            <el-radio label="DENY">拒绝（无规则时默认拒绝）</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 规则关联表格 -->
    <el-card class="rule-relations">
      <template #header>
        <span>规则关联配置</span>
      </template>

      <el-table :data="ruleRelations" border>
        <el-table-column prop="ruleName" label="规则名称" width="200">
          <template #default="{ row }">
            <el-select v-model="row.ruleId" placeholder="选择规则" @change="updateRuleName(row)">
              <el-option
                v-for="rule in availableRules"
                :key="rule.ruleId"
                :label="rule.ruleName"
                :value="rule.ruleId"
              />
            </el-select>
          </template>
        </el-table-column>

        <!-- 责任链方式：显示执行顺序 -->
        <el-table-column
          v-if="configForm.executionType === 'CHAIN'"
          prop="executionOrder"
          label="执行顺序"
          width="120">
          <template #default="{ row }">
            <el-input-number
              v-model="row.executionOrder"
              :min="1"
              :max="100"
              size="small"
            />
          </template>
        </el-table-column>

        <!-- 逻辑运算方式：显示逻辑操作符 -->
        <el-table-column
          v-if="configForm.executionType === 'LOGIC'"
          prop="operator"
          label="逻辑操作符"
          width="120">
          <template #default="{ row }">
            <el-select v-model="row.operator" size="small">
              <el-option label="AND（与）" value="AND" />
              <el-option label="OR（或）" value="OR" />
              <el-option label="NOT（非）" value="NOT" />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column prop="required" label="必需执行" width="100">
          <template #default="{ row }">
            <el-switch v-model="row.required" />
          </template>
        </el-table-column>

        <el-table-column prop="timeout" label="超时时间(ms)" width="140">
          <template #default="{ row }">
            <el-input-number
              v-model="row.timeout"
              :min="100"
              :max="30000"
              size="small"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150">
          <template #default="{ row, $index }">
            <el-button size="small" @click="moveUp($index)" :disabled="$index === 0">
              上移
            </el-button>
            <el-button size="small" @click="moveDown($index)" :disabled="$index === ruleRelations.length - 1">
              下移
            </el-button>
            <el-button size="small" type="danger" @click="removeRule($index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="table-footer">
        <el-button @click="addRule">添加规则</el-button>
        <el-button @click="batchImport">批量导入</el-button>
        <el-button @click="previewLogic">预览执行逻辑</el-button>
      </div>
    </el-card>

    <!-- 执行逻辑预览 -->
    <el-card v-if="showLogicPreview" class="logic-preview">
      <template #header>
        <span>执行逻辑预览</span>
      </template>

      <div class="logic-description">
        <p><strong>执行模式：</strong>{{ configForm.executionMode === 'SHORT_CIRCUIT' ? '短路执行' : '全部执行' }}</p>
        <p><strong>执行顺序：</strong></p>
        <ol>
          <li v-for="(relation, index) in sortedRuleRelations" :key="index">
            {{ relation.ruleName }}
            <el-tag size="small" :type="getOperatorType(relation.operator)">
              {{ getOperatorText(relation.operator) }}
            </el-tag>
            <el-tag v-if="relation.required" size="small" type="warning">必需</el-tag>
          </li>
        </ol>
        <p><strong>逻辑说明：</strong>{{ getLogicDescription() }}</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'RuleRelationConfig',
  data() {
    return {
      configForm: {
        messageCode: '',
        dialogType: '',
        executionMode: 'SHORT_CIRCUIT',
        defaultAction: 'ALLOW'
      },
      ruleRelations: [],
      availableRules: [],
      showLogicPreview: false
    }
  },
  computed: {
    sortedRuleRelations() {
      return [...this.ruleRelations].sort((a, b) => a.executionOrder - b.executionOrder);
    }
  },
  methods: {
    addRule() {
      this.ruleRelations.push({
        ruleId: '',
        ruleName: '',
        executionOrder: this.ruleRelations.length + 1,
        operator: 'AND',
        required: false,
        timeout: 5000
      });
    },

    removeRule(index) {
      this.ruleRelations.splice(index, 1);
      this.reorderRules();
    },

    moveUp(index) {
      if (index > 0) {
        const temp = this.ruleRelations[index].executionOrder;
        this.ruleRelations[index].executionOrder = this.ruleRelations[index - 1].executionOrder;
        this.ruleRelations[index - 1].executionOrder = temp;
      }
    },

    moveDown(index) {
      if (index < this.ruleRelations.length - 1) {
        const temp = this.ruleRelations[index].executionOrder;
        this.ruleRelations[index].executionOrder = this.ruleRelations[index + 1].executionOrder;
        this.ruleRelations[index + 1].executionOrder = temp;
      }
    },

    previewLogic() {
      this.showLogicPreview = !this.showLogicPreview;
    },

    getOperatorType(operator) {
      const types = { 'AND': 'success', 'OR': 'warning', 'NOT': 'danger' };
      return types[operator] || 'info';
    },

    getOperatorText(operator) {
      const texts = { 'AND': '与', 'OR': '或', 'NOT': '非' };
      return texts[operator] || operator;
    },

    onExecutionTypeChange(newType) {
      // 执行方式变更时，重置规则关联配置
      if (newType === 'CHAIN') {
        // 责任链方式：重置为执行顺序
        this.ruleRelations.forEach((relation, index) => {
          relation.executionOrder = index + 1;
          relation.operator = 'AND'; // 责任链不使用逻辑操作符
        });
      } else if (newType === 'LOGIC') {
        // 逻辑运算方式：重置为逻辑操作符
        this.ruleRelations.forEach(relation => {
          relation.operator = relation.operator || 'AND';
          relation.executionOrder = 0; // 逻辑运算不使用执行顺序
        });
      }
    },

    getLogicDescription() {
      if (this.configForm.executionType === 'CHAIN') {
        return '责任链方式：按顺序执行规则，任一规则拒绝则立即停止，不再执行后续规则。适合简单过滤场景。';
      } else if (this.configForm.executionType === 'LOGIC') {
        return '逻辑运算方式：执行所有规则，根据逻辑操作符（AND/OR/NOT）综合判断最终结果。适合复杂逻辑场景。';
      }
      return '';
    }
  }
}
</script>
```

## 6. 监控统计界面

### 6.1 执行统计页面
```vue
<template>
  <div class="execution-statistics">
    <div class="stats-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ overviewStats.totalExecutions }}</div>
              <div class="stat-label">总执行次数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ overviewStats.successRate }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ overviewStats.avgExecutionTime }}ms</div>
              <div class="stat-label">平均执行时间</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ overviewStats.activePlugins }}</div>
              <div class="stat-label">活跃插件数</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 图表展示 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>执行趋势</span>
          </template>
          <div ref="executionTrendChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>插件使用分布</span>
          </template>
          <div ref="pluginUsageChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 详细统计表格 -->
    <el-card class="detail-stats">
      <template #header>
        <span>详细统计</span>
      </template>
      <el-table :data="detailStats" v-loading="loading">
        <el-table-column prop="pluginName" label="插件名称" />
        <el-table-column prop="ruleName" label="规则名称" />
        <el-table-column prop="executionCount" label="执行次数" />
        <el-table-column prop="successCount" label="成功次数" />
        <el-table-column prop="failureCount" label="失败次数" />
        <el-table-column prop="avgExecutionTime" label="平均执行时间(ms)" />
      </el-table>
    </el-card>
  </div>
</template>
```

## 7. 响应式设计

### 7.1 移动端适配
```scss
// 响应式断点
$mobile: 768px;
$tablet: 1024px;
$desktop: 1200px;

.plugin-management {
  @media (max-width: $mobile) {
    .page-header {
      flex-direction: column;
      gap: 10px;
      
      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }
    
    .search-bar {
      .el-form--inline .el-form-item {
        display: block;
        margin-right: 0;
        margin-bottom: 10px;
      }
    }
    
    .el-table {
      font-size: 12px;
      
      .el-table__cell {
        padding: 8px 5px;
      }
    }
  }
}
```

### 7.2 主题定制
```scss
// 主题变量
:root {
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  
  --bg-color: #f5f5f5;
  --card-bg: #ffffff;
  --border-color: #dcdfe6;
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
}

// 暗色主题
[data-theme="dark"] {
  --bg-color: #1a1a1a;
  --card-bg: #2d2d2d;
  --border-color: #4c4d4f;
  --text-primary: #e5eaf3;
  --text-regular: #cfd3dc;
  --text-secondary: #a3a6ad;
}
```

## 8. 总结

本配置管理界面设计提供了完整的插件式过滤系统管理功能，包括：

1. **插件管理**: 插件上传、配置、启用/禁用等
2. **规则管理**: 规则创建、配置、测试等
3. **过滤器管理**: 可视化规则编排和组合
4. **监控统计**: 实时监控和统计分析
5. **响应式设计**: 支持多种设备和屏幕尺寸

界面设计注重用户体验，提供直观的操作流程和丰富的功能特性，能够满足不同用户的管理需求。
