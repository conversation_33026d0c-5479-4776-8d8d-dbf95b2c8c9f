# 02-架构设计-插件式过滤系统架构设计

## 1. 总体架构

### 1.1 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    配置管理界面层                              │
├─────────────────────────────────────────────────────────────┤
│  插件管理界面  │  规则配置界面  │  过滤器管理界面  │  监控界面   │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      API接口层                               │
├─────────────────────────────────────────────────────────────┤
│  插件管理API   │  规则管理API   │  过滤器API      │  监控API    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     核心服务层                               │
├─────────────────────────────────────────────────────────────┤
│           过滤器管理器 (FilterManager)                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  插件管理器      │  │  规则编排引擎    │  │  配置管理器   │ │
│  │ PluginManager   │  │RuleOrchestrator │  │ConfigManager │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     插件层                                   │
├─────────────────────────────────────────────────────────────┤
│ TimeFilter │ UserFilter │ BusinessFilter │ FrequencyFilter  │
│   Plugin   │   Plugin   │     Plugin     │     Plugin       │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层                                │
├─────────────────────────────────────────────────────────────┤
│ 插件配置表 │ 规则配置表 │ 过滤器组表 │ 规则关联表 │ 执行日志表 │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 架构特点

- **分层架构**: 清晰的分层设计，职责分离
- **插件化**: 核心功能通过插件扩展
- **可配置**: 支持动态配置和热更新
- **可监控**: 完整的执行统计和性能监控
- **可扩展**: 支持第三方插件集成

## 2. 核心组件设计

### 2.1 过滤器管理器 (FilterManager)

#### 2.1.1 职责
- 统一的过滤入口
- 协调各个组件工作
- 管理过滤执行流程
- 提供缓存和性能优化

#### 2.1.2 核心接口
```java
public interface FilterManager {
    /**
     * 执行过滤逻辑
     */
    FilterResult executeFilter(FilterContext context);
    
    /**
     * 获取过滤器组配置
     */
    FilterGroup getFilterGroup(String messageCode, String dialogType);
    
    /**
     * 刷新配置缓存
     */
    void refreshCache();
    
    /**
     * 获取执行统计
     */
    FilterStatistics getStatistics();
}
```

#### 2.1.3 实现要点
- 支持配置缓存和热更新
- 提供异步执行能力
- 实现降级和容错机制
- 记录详细的执行日志

### 2.2 插件管理器 (PluginManager)

#### 2.2.1 职责
- 插件的加载和注册
- 插件生命周期管理
- 插件依赖管理
- 插件异常处理

#### 2.2.2 核心接口
```java
public interface PluginManager {
    /**
     * 注册插件
     */
    void registerPlugin(FilterRulePlugin plugin);
    
    /**
     * 获取插件实例
     */
    FilterRulePlugin getPlugin(String pluginId);
    
    /**
     * 卸载插件
     */
    void unloadPlugin(String pluginId);
    
    /**
     * 获取所有插件
     */
    List<FilterRulePlugin> getAllPlugins();
    
    /**
     * 插件健康检查
     */
    PluginHealth checkPluginHealth(String pluginId);
}
```

#### 2.2.3 统一插件加载机制

系统采用基于反射的统一插件加载机制，支持两种实现方式：

##### 标准接口实现方式（推荐）
```java
@Plugin(
    id = "basic_switch_filter",
    name = "基础开关过滤插件",
    version = "1.0.0",
    author = "system"
)
public class BasicSwitchFilterPlugin implements FilterRulePlugin {
    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        // 标准化实现
        Boolean enabled = (Boolean) config.get("enabled");
        return FilterResult.builder()
            .allowed(enabled != null ? enabled : true)
            .reason(enabled ? "开关已启用" : "开关已禁用")
            .pluginId("basic_switch_filter")
            .build();
    }
}
```

##### 自定义方法实现方式（兼容性）
```java
public class LegacyCustomFilter {
    public boolean checkPermission(FilterContext context, Map<String, Object> config) {
        // 任意方法签名，通过反射适配器调用
        String userId = context.getUserId();
        return hasPermission(userId);
    }

    public FilterResult customCheck(Map<String, Object> params) {
        // 支持多种返回类型，自动适配
        return FilterResult.builder()
            .allowed(true)
            .reason("自定义检查通过")
            .build();
    }
}
```

##### 统一加载器实现
```java
@Component
public class UnifiedPluginLoader {

    public FilterRulePlugin loadPlugin(PluginMetadata metadata) {
        if (PluginType.BUILTIN.equals(metadata.getPluginType())) {
            return loadBuiltinPlugin(metadata);
        } else {
            return loadCustomPlugin(metadata);
        }
    }

    private FilterRulePlugin loadCustomPlugin(PluginMetadata metadata) throws Exception {
        // 1. 创建插件专用类加载器
        PluginClassLoader classLoader = createClassLoader(metadata.getJarPath());

        // 2. 加载插件类
        Class<?> pluginClass = classLoader.loadClass(metadata.getPluginClass());
        Object instance = pluginClass.newInstance();

        // 3. 检查是否实现标准接口
        if (instance instanceof FilterRulePlugin) {
            return (FilterRulePlugin) instance;
        } else {
            // 4. 使用反射适配器包装
            return new ReflectionPluginAdapter(metadata, instance, classLoader);
        }
    }
}
```

#### 2.2.4 插件发现机制

系统采用多种方式发现和识别插件：

##### 内置插件发现
```java
@Component
public class BuiltinPluginDiscovery {

    /**
     * 通过注解扫描发现内置插件
     */
    public List<PluginMetadata> discoverBuiltinPlugins() {
        List<PluginMetadata> plugins = new ArrayList<>();

        // 扫描指定包下的@Plugin注解类
        Set<Class<?>> pluginClasses = scanPluginClasses("com.company.filter.builtin");

        for (Class<?> pluginClass : pluginClasses) {
            Plugin annotation = pluginClass.getAnnotation(Plugin.class);
            if (annotation != null) {
                PluginMetadata metadata = PluginMetadata.builder()
                    .pluginId(annotation.id())
                    .pluginName(annotation.name())
                    .pluginType(PluginType.BUILTIN)
                    .pluginClass(pluginClass.getName())
                    .pluginMethod("execute") // 标准方法名
                    .version(annotation.version())
                    .author(annotation.author())
                    .build();

                plugins.add(metadata);
            }
        }

        return plugins;
    }
}
```

##### 自定义插件发现
```java
@Component
public class CustomPluginDiscovery {

    /**
     * 从数据库发现自定义插件配置
     */
    public List<PluginMetadata> discoverCustomPlugins() {
        List<FilterPlugin> plugins = pluginRepository.findByPluginTypeAndPluginStatus(
            PluginType.CUSTOM, PluginStatus.ENABLED);

        return plugins.stream()
            .map(this::convertToMetadata)
            .collect(Collectors.toList());
    }
}
```

#### 2.2.5 反射适配器设计

为了支持任意类和方法的插件实现，系统提供反射适配器：

```java
public class ReflectionPluginAdapter implements FilterRulePlugin {

    private final PluginMetadata metadata;
    private final Object pluginInstance;
    private final Method executeMethod;
    private final ClassLoader classLoader;

    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        // 设置线程上下文类加载器
        ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
        try {
            Thread.currentThread().setContextClassLoader(classLoader);

            // 通过反射调用插件方法
            Object result = executeMethod.invoke(pluginInstance, context, config);

            // 适配返回结果
            return adaptResult(result);

        } catch (Exception e) {
            return FilterResult.builder()
                .allowed(false)
                .reason("插件执行异常: " + e.getMessage())
                .pluginId(metadata.getPluginId())
                .build();
        } finally {
            Thread.currentThread().setContextClassLoader(originalClassLoader);
        }
    }

    /**
     * 适配不同类型的返回结果
     */
    private FilterResult adaptResult(Object result) {
        if (result instanceof FilterResult) {
            return (FilterResult) result;
        } else if (result instanceof Boolean) {
            return FilterResult.builder()
                .allowed((Boolean) result)
                .reason("插件返回: " + result)
                .pluginId(metadata.getPluginId())
                .build();
        } else if (result instanceof String) {
            return FilterResult.builder()
                .allowed(false)
                .reason((String) result)
                .pluginId(metadata.getPluginId())
                .build();
        } else {
            return FilterResult.builder()
                .allowed(true)
                .reason("插件执行成功")
                .pluginId(metadata.getPluginId())
                .build();
        }
    }
}
```

### 2.3 规则编排引擎 (RuleOrchestrator)

#### 2.3.1 职责
- 解析规则编排配置
- 执行复杂的逻辑组合
- 支持短路执行优化
- 管理执行上下文

#### 2.3.2 核心接口
```java
public interface RuleOrchestrator {
    /**
     * 执行规则编排
     */
    OrchestrationResult execute(
        OrchestrationConfig config, 
        FilterContext context
    );
    
    /**
     * 验证编排配置
     */
    ValidationResult validate(OrchestrationConfig config);
    
    /**
     * 解析编排表达式
     */
    ExpressionTree parseExpression(String expression);
}
```

#### 2.3.3 编排配置格式
```json
{
  "mode": "SHORT_CIRCUIT",
  "timeout": 5000,
  "expression": {
    "operator": "AND",
    "children": [
      {
        "operator": "OR",
        "children": [
          {
            "ruleId": "time_rule_work_hours",
            "weight": 1.0,
            "timeout": 1000
          },
          {
            "ruleId": "time_rule_maintenance",
            "weight": 0.8,
            "timeout": 1000
          }
        ]
      },
      {
        "operator": "NOT",
        "children": [
          {
            "ruleId": "user_rule_blacklist",
            "weight": 1.0,
            "timeout": 500
          }
        ]
      }
    ]
  }
}
```

### 2.4 配置管理器 (ConfigManager)

#### 2.4.1 职责
- 配置的CRUD操作
- 配置验证和校验
- 配置变更通知
- 配置版本管理

#### 2.4.2 核心接口
```java
public interface ConfigManager {
    /**
     * 保存过滤器组配置
     */
    void saveFilterGroup(FilterGroup filterGroup);
    
    /**
     * 获取过滤器组配置
     */
    FilterGroup getFilterGroup(String groupId);
    
    /**
     * 保存规则配置
     */
    void saveRule(FilterRule rule);
    
    /**
     * 获取规则配置
     */
    FilterRule getRule(String ruleId);
    
    /**
     * 配置变更监听
     */
    void addConfigChangeListener(ConfigChangeListener listener);
}
```

## 3. 插件系统设计

### 3.1 插件接口定义

#### 3.1.1 核心插件接口
```java
public interface FilterRulePlugin {
    /**
     * 插件唯一标识
     */
    String getPluginId();
    
    /**
     * 插件名称
     */
    String getPluginName();
    
    /**
     * 插件版本
     */
    String getVersion();
    
    /**
     * 插件描述
     */
    String getDescription();
    
    /**
     * 执行过滤逻辑
     */
    FilterResult execute(FilterContext context, Map<String, Object> config);
    
    /**
     * 验证配置参数
     */
    ValidationResult validate(Map<String, Object> config);
    
    /**
     * 获取默认配置
     */
    Map<String, Object> getDefaultConfig();
    
    /**
     * 获取配置模式定义
     */
    ConfigSchema getConfigSchema();
    
    /**
     * 插件初始化
     */
    void initialize() throws PluginException;
    
    /**
     * 插件销毁
     */
    void destroy() throws PluginException;
}
```

#### 3.1.2 过滤上下文
```java
public class FilterContext {
    private String messageCode;      // 消息标识
    private String dialogType;       // 弹窗类型
    private String userId;           // 用户ID
    private Map<String, Object> messageData;  // 消息数据
    private Map<String, Object> userContext;  // 用户上下文
    private Map<String, Object> systemContext; // 系统上下文
    private long timestamp;          // 时间戳
    
    // getter/setter methods
}
```

#### 3.1.3 过滤结果
```java
public class FilterResult {
    private boolean allowed;         // 是否允许
    private String reason;           // 过滤原因
    private Map<String, Object> metadata; // 元数据
    private long executionTime;      // 执行时间
    private String pluginId;         // 插件ID
    
    // getter/setter methods
}
```

### 3.2 内置插件设计

**设计原则**: 内置插件由系统预置和管理，使用@Plugin注解和标准FilterRulePlugin接口，通过注解扫描自动发现和注册。

#### 3.2.1 内置插件实现规范

所有内置插件必须遵循以下规范：
1. 使用@Plugin注解标识插件元数据
2. 实现FilterRulePlugin标准接口
3. 放置在com.company.filter.builtin包下
4. 使用@Component注解支持Spring管理

#### 3.2.2 基础开关过滤插件 (BasicSwitchFilterPlugin)
```java
@Plugin(
    id = "basic_switch_filter",
    name = "基础开关过滤插件",
    version = "1.0.0",
    author = "system",
    description = "提供基础的开关控制功能"
)
@Component
public class BasicSwitchFilterPlugin implements FilterRulePlugin {

    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        Boolean enabled = (Boolean) config.get("enabled");

        return FilterResult.builder()
            .allowed(enabled != null ? enabled : true)
            .reason(enabled ? "开关已启用" : "开关已禁用")
            .pluginId("basic_switch_filter")
            .executionTime(System.currentTimeMillis() - context.getStartTime())
            .build();
    }

    @Override
    public PluginMetadata getMetadata() {
        Plugin annotation = this.getClass().getAnnotation(Plugin.class);
        return PluginMetadata.fromAnnotation(annotation);
    }
}
```

#### 3.2.3 增强时间段过滤插件 (EnhancedTimeFilterPlugin)
```java
@Plugin(
    id = "enhanced_time_filter",
    name = "增强时间段过滤插件",
    version = "1.0.0",
    author = "system",
    description = "支持跨天时间段的高级时间过滤功能"
)
@Component
public class EnhancedTimeFilterPlugin implements FilterRulePlugin {

    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        String startTime = (String) config.get("startTime");
        String endTime = (String) config.get("endTime");
        Boolean allowCrossDay = (Boolean) config.get("allowCrossDay");
        String[] weekdays = (String[]) config.get("weekdays");

        // 检查工作日
        if (weekdays != null && weekdays.length > 0) {
            String currentDay = LocalDate.now().getDayOfWeek().toString();
            if (!Arrays.asList(weekdays).contains(currentDay)) {
                return FilterResult.builder()
                    .allowed(false)
                    .reason("不在允许的工作日内")
                    .pluginId("enhanced_time_filter")
                    .build();
            }
        }

        // 检查时间段
        LocalTime now = LocalTime.now();
        LocalTime start = LocalTime.parse(startTime);
        LocalTime end = LocalTime.parse(endTime);

        boolean allowed;
        if (allowCrossDay != null && allowCrossDay && start.isAfter(end)) {
            // 跨天时间段：如 22:00-06:00
            allowed = now.isAfter(start) || now.isBefore(end);
        } else {
            // 正常时间段：如 09:00-18:00
            allowed = now.isAfter(start) && now.isBefore(end);
        }

        return FilterResult.builder()
            .allowed(allowed)
            .reason(allowed ? "在允许时间段内" : "不在允许时间段内")
            .pluginId("enhanced_time_filter")
            .executionTime(System.currentTimeMillis() - context.getStartTime())
            .build();
    }

    @Override
    public PluginMetadata getMetadata() {
        Plugin annotation = this.getClass().getAnnotation(Plugin.class);
        return PluginMetadata.fromAnnotation(annotation);
    }
}
```

#### 3.2.2 用户过滤插件 (UserFilterPlugin)
```java
@Plugin(id = "user_filter", name = "用户过滤插件")
public class UserFilterPlugin implements FilterRulePlugin {
    
    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        String[] allowedRoles = (String[]) config.get("allowedRoles");
        String[] blockedUsers = (String[]) config.get("blockedUsers");
        
        // 执行用户过滤逻辑
        boolean allowed = isUserAllowed(context.getUserId(), allowedRoles, blockedUsers);
        
        return FilterResult.builder()
            .allowed(allowed)
            .reason(allowed ? "用户权限允许" : "用户权限不足或被阻止")
            .pluginId(getPluginId())
            .build();
    }
}
```

#### 3.2.3 基础开关过滤插件 (BasicSwitchPlugin)
```java
@Plugin(id = "basic_switch_filter", name = "基础开关过滤插件")
public class BasicSwitchPlugin implements FilterRulePlugin {

    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        Boolean enabled = (Boolean) config.get("enabled");

        return FilterResult.builder()
            .allowed(enabled != null ? enabled : true)
            .reason(enabled ? "开关已启用" : "开关已禁用")
            .pluginId(getPluginId())
            .build();
    }
}
```

#### 3.2.4 重复规则过滤插件 (RepeatRulePlugin)
```java
@Plugin(id = "repeat_rule_filter", name = "重复规则过滤插件")
public class RepeatRulePlugin implements FilterRulePlugin {

    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        String repeatType = (String) config.get("repeatType");
        LocalDateTime executeTime = (LocalDateTime) config.get("executeTime");

        boolean shouldExecute = checkRepeatRule(repeatType, executeTime);

        return FilterResult.builder()
            .allowed(shouldExecute)
            .reason(getRepeatReason(repeatType, shouldExecute))
            .pluginId(getPluginId())
            .build();
    }
}
```

#### 3.2.5 频率限制过滤插件 (FrequencyLimitPlugin)
```java
@Plugin(id = "frequency_limit_filter", name = "频率限制过滤插件")
public class FrequencyLimitPlugin implements FilterRulePlugin {

    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        Integer timeWindow = (Integer) config.get("timeWindow");
        Integer maxCount = (Integer) config.get("maxCount");

        boolean withinLimit = checkFrequencyLimit(context, timeWindow, maxCount);

        return FilterResult.builder()
            .allowed(withinLimit)
            .reason(withinLimit ? "频率正常" : "超出频率限制")
            .pluginId(getPluginId())
            .build();
    }
}
```

### 3.3 超时机制设计

#### 3.3.1 三层超时体系
系统采用三层超时控制机制，确保执行时间的合理分配和故障隔离：

```java
// 1. 插件级超时 (Plugin Level Timeout)
@Entity
public class FilterPlugin {
    private String pluginId;
    private Integer timeout = 2000; // 插件执行的最大允许时间(ms)
    // 内置插件：系统设定默认值
    // 自定义插件：可配置，建议根据业务复杂度设置
}

// 2. 规则级超时 (Rule Level Timeout)
@Entity
public class FilterRule {
    private String ruleId;
    private String pluginId;
    private Integer timeout = 1500; // 规则执行的最大时间(ms)
    // 约束：规则超时 ≤ 插件超时
}

// 3. 过滤器组级总超时 (Filter Group Total Timeout)
@Entity
public class FilterGroup {
    private String groupId;
    private Integer totalTimeout = 5000; // 整个勿扰配置的总超时(ms)
    // 用于责任链执行时的总时间控制
}
```

#### 3.3.2 超时约束关系
```
约束规则：规则超时 ≤ 插件超时 ≤ 总超时
示例：    1500ms   ≤   2000ms   ≤  5000ms

责任链执行时的超时分配：
总超时 = 5000ms
├── 规则1: min(规则超时1500ms, 插件超时2000ms, 剩余时间5000ms) = 1500ms
├── 规则2: min(规则超时1200ms, 插件超时1800ms, 剩余时间3500ms) = 1200ms
└── 规则3: min(规则超时1000ms, 插件超时1500ms, 剩余时间2300ms) = 1000ms
```

### 3.4 优先级与执行顺序机制

#### 3.4.1 双层排序体系
```java
// 1. 规则优先级 (Rule Priority) - 全局重要程度
@Entity
public class FilterRule {
    private String ruleId;
    private Integer priority = 1; // 数字越小优先级越高，全局有效
    // 用途：规则重要程度标识、默认排序依据、冲突解决
}

// 2. 执行顺序 (Execution Order) - 组内执行先后
@Entity
public class FilterGroupRule {
    private String groupId;
    private String ruleId;
    private Integer executionOrder = 1; // 在该组中的执行顺序
    private String logicOperator = "AND"; // 逻辑操作符
    // 用途：责任链执行的实际顺序，可覆盖优先级
}
```

#### 3.4.2 优先级与执行顺序的关系
```
规则优先级 (Rule Priority):
├── 作用：规则的重要程度标识
├── 范围：全局性，跨组有效
├── 用途：默认排序依据、冲突解决
└── 示例：priority=1 比 priority=2 重要

执行顺序 (Execution Order):
├── 作用：在特定勿扰配置中的执行先后
├── 范围：组内有效，可覆盖优先级
├── 用途：责任链执行的实际顺序
└── 示例：order=1 先执行，order=2 后执行

实际应用：
规则A: 基础开关规则 (priority=1, 全局最重要)
规则B: 时间段规则   (priority=2)
规则C: 用户权限规则 (priority=3)

在"系统告警勿扰配置"中的执行顺序可能是：
1. 时间段规则     (executionOrder=1) - 先检查时间，快速过滤
2. 用户权限规则   (executionOrder=2) - 再检查权限
3. 基础开关规则   (executionOrder=3) - 最后检查开关
```

### 3.5 统一插件生命周期管理

#### 3.5.1 插件生命周期状态

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  DISCOVERY  │───▶│   LOADING   │───▶│   ACTIVE    │───▶│ DESTROYING  │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │                   │
       ▼                   ▼                   ▼                   ▼
   发现插件配置         加载插件类           提供服务           调用destroy()
   验证插件元数据       创建插件实例         处理过滤请求       清理资源
   注册到发现列表       调用initialize()     监控执行状态       注销插件
```

#### 3.5.2 标准接口插件生命周期

```java
@Component
public class StandardPluginLifecycleManager {

    public void managePluginLifecycle(PluginMetadata metadata) {
        try {
            // 1. DISCOVERY - 发现阶段
            log.info("Discovering plugin: {}", metadata.getPluginId());
            validatePluginMetadata(metadata);

            // 2. LOADING - 加载阶段
            log.info("Loading plugin: {}", metadata.getPluginId());
            FilterRulePlugin instance = loadStandardPlugin(metadata);

            // 3. INITIALIZING - 初始化阶段
            log.info("Initializing plugin: {}", metadata.getPluginId());
            instance.initialize();

            // 4. ACTIVE - 激活阶段
            pluginRegistry.registerPlugin(metadata.getPluginId(), instance);
            log.info("Plugin activated: {}", metadata.getPluginId());

        } catch (Exception e) {
            log.error("Plugin lifecycle management failed: {}", metadata.getPluginId(), e);
            throw new PluginLifecycleException("Failed to manage plugin lifecycle", e);
        }
    }
}
```

#### 3.5.3 反射适配器插件生命周期

```java
@Component
public class ReflectionPluginLifecycleManager {

    public void manageReflectionPluginLifecycle(PluginMetadata metadata) {
        try {
            // 1. DISCOVERY - 发现阶段
            log.info("Discovering reflection plugin: {}", metadata.getPluginId());
            validateReflectionPlugin(metadata);

            // 2. LOADING - 加载阶段
            log.info("Loading reflection plugin: {}", metadata.getPluginId());
            PluginClassLoader classLoader = createClassLoader(metadata.getJarPath());
            Class<?> pluginClass = classLoader.loadClass(metadata.getPluginClass());
            Object instance = pluginClass.newInstance();

            // 3. WRAPPING - 包装阶段
            log.info("Wrapping reflection plugin: {}", metadata.getPluginId());
            ReflectionPluginAdapter adapter = new ReflectionPluginAdapter(
                metadata, instance, classLoader);

            // 4. ACTIVE - 激活阶段
            pluginRegistry.registerPlugin(metadata.getPluginId(), adapter);
            log.info("Reflection plugin activated: {}", metadata.getPluginId());

        } catch (Exception e) {
            log.error("Reflection plugin lifecycle management failed: {}",
                metadata.getPluginId(), e);
            throw new PluginLifecycleException("Failed to manage reflection plugin lifecycle", e);
        }
    }
}
```

## 4. 数据模型设计

### 4.1 核心实体

#### 4.1.1 过滤器组 (FilterGroup)
```java
public class FilterGroup {
    private String groupId;              // 组ID
    private String groupName;            // 组名称
    private String messageCode;          // 消息标识
    private String dialogType;           // 弹窗类型
    private OrchestrationConfig orchestration; // 编排配置
    private ExecutionMode executionMode; // 执行模式
    private GroupStatus status;          // 状态
    private Date createTime;             // 创建时间
    private Date updateTime;             // 更新时间
}
```

#### 4.1.2 过滤规则 (FilterRule)
```java
public class FilterRule {
    private String ruleId;               // 规则ID
    private String ruleName;             // 规则名称
    private String pluginId;             // 插件ID
    private Map<String, Object> config;  // 规则配置
    private RuleStatus status;           // 状态
    private Date createTime;             // 创建时间
    private Date updateTime;             // 更新时间
}
```

#### 4.1.3 插件信息 (PluginInfo)
```java
public class PluginInfo {
    private String pluginId;             // 插件ID
    private String pluginName;           // 插件名称
    private String pluginClass;          // 插件类名
    private String version;              // 版本
    private PluginStatus status;         // 状态
    private Map<String, Object> config;  // 插件配置
    private Date createTime;             // 创建时间
    private Date updateTime;             // 更新时间
}
```

### 4.2 关系模型

```
FilterGroup (1) ──── (N) RuleRelation (N) ──── (1) FilterRule
     │                                                │
     │                                                │
     └── messageCode/dialogType                       └── pluginId
                                                      │
                                                      │
                                               (1) PluginInfo
```

## 5. 技术选型

### 5.1 核心技术栈
- **Java 8+**: 基础开发语言
- **Spring Framework**: 依赖注入和AOP
- **MyBatis**: 数据持久化
- **Jackson**: JSON序列化
- **SLF4J + Logback**: 日志框架

### 5.2 插件技术
- **Java SPI**: 插件发现机制
- **Reflection**: 动态加载和调用
- **ClassLoader**: 插件隔离
- **Annotation**: 插件元数据

### 5.3 配置技术
- **JSON**: 配置存储格式
- **Bean Validation**: 配置验证
- **Spring Cache**: 配置缓存
- **Event**: 配置变更通知

## 6. 性能设计

### 6.1 缓存策略
- **配置缓存**: 缓存过滤器组和规则配置
- **结果缓存**: 缓存规则执行结果
- **插件缓存**: 缓存插件实例
- **多级缓存**: 本地缓存 + 分布式缓存

### 6.2 执行优化
- **短路执行**: AND遇false停止，OR遇true停止
- **并行执行**: 独立规则并行执行
- **异步执行**: 非关键规则异步执行
- **超时控制**: 防止规则执行时间过长

### 6.3 监控指标
- **执行时间**: 规则和整体执行时间
- **成功率**: 规则执行成功率
- **缓存命中率**: 各级缓存命中率
- **并发量**: 同时执行的过滤请求数

## 7. 安全设计

### 7.1 插件安全
- **权限控制**: 限制插件访问系统资源
- **沙箱隔离**: 插件运行在受限环境中
- **代码审查**: 插件代码安全审查
- **签名验证**: 插件包数字签名验证

### 7.2 配置安全
- **权限管理**: 配置操作权限控制
- **审计日志**: 记录所有配置变更
- **数据加密**: 敏感配置数据加密
- **备份恢复**: 配置数据备份和恢复

## 8. 部署架构

### 8.1 单机部署
```
┌─────────────────────────────────────┐
│           应用服务器                 │
│  ┌─────────────────────────────────┐ │
│  │        Filter Core              │ │
│  │  ┌─────────┐  ┌─────────────┐   │ │
│  │  │ Plugin1 │  │   Plugin2   │   │ │
│  │  └─────────┘  └─────────────┘   │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
              │
┌─────────────────────────────────────┐
│            数据库                   │
└─────────────────────────────────────┘
```

### 8.2 集群部署
```
┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│   Node 1    │  │   Node 2    │  │   Node 3    │
│ Filter Core │  │ Filter Core │  │ Filter Core │
└─────────────┘  └─────────────┘  └─────────────┘
       │                │                │
       └────────────────┼────────────────┘
                        │
┌─────────────────────────────────────────────────┐
│              共享存储 (数据库 + 缓存)              │
└─────────────────────────────────────────────────┘
```

## 9. 总结

本架构设计采用分层、插件化的设计思想，具有以下优势：

- **高扩展性**: 通过插件机制支持功能扩展
- **高可配置性**: 支持复杂的规则编排和配置
- **高性能**: 多种优化策略保证执行效率
- **高可用性**: 完善的容错和降级机制
- **易维护性**: 清晰的分层和模块化设计

该架构能够满足业务对过滤系统的各种需求，同时为未来的功能扩展预留了充分的空间。
