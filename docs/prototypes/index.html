<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件式过滤系统 - 原型图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .prototype-nav {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .prototype-nav h2 {
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .prototype-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .prototype-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
        }
        
        .prototype-card .icon {
            font-size: 3em;
            margin-bottom: 15px;
            color: #667eea;
        }
        
        .prototype-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.3em;
        }
        
        .prototype-card p {
            color: #666;
            font-size: 0.9em;
            line-height: 1.5;
        }
        
        .prototype-card .badge {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            margin-top: 10px;
        }
        
        .back-link {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-bottom: 20px;
            transition: background 0.3s ease;
        }
        
        .back-link:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            color: #666;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 30px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .prototype-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="../index.html" class="back-link">← 返回文档首页</a>
        
        <div class="header">
            <h1>插件式过滤系统原型图</h1>
            <p>基于表格+表单设计的管理界面原型</p>
        </div>
        


        <div class="prototype-nav">
            <h2>🎨 界面原型导航</h2>
            <div class="prototype-grid">
                <div class="prototype-card" onclick="openPrototype('plugin-management.html')">
                    <div class="icon">🔌</div>
                    <h3>插件管理</h3>
                    <p>插件列表、上传、配置、启用/禁用等功能的表格化管理界面</p>
                    <div class="badge">表格主导</div>
                </div>
                
                <div class="prototype-card" onclick="openPrototype('rule-management.html')">
                    <div class="icon">📋</div>
                    <h3>规则管理</h3>
                    <p>过滤规则的创建、编辑、测试，动态表单配置界面</p>
                    <div class="badge">表格+表单</div>
                </div>
                
                <div class="prototype-card" onclick="openPrototype('filter-group-management.html')">
                    <div class="icon">🔕</div>
                    <h3>勿扰模式配置</h3>
                    <p>勿扰模式配置，过滤规则管理，智能弹窗控制的表格界面</p>
                    <div class="badge">表格主导</div>
                </div>
                
                <div class="prototype-card" onclick="openPrototype('rule-relation-config.html')">
                    <div class="icon">🔗</div>
                    <h3>勿扰规则配置</h3>
<parameter name="p">配置勿扰模式中过滤规则的执行顺序和逻辑关系</p>
                    <div class="badge">表格+表单</div>
                </div>
                
                <div class="prototype-card" onclick="openPrototype('monitoring-dashboard.html')">
                    <div class="icon">📊</div>
                    <h3>监控统计</h3>
                    <p>执行统计、性能监控、错误日志的表格化展示界面</p>
                    <div class="badge">表格+图表</div>
                </div>
                
                <div class="prototype-card" onclick="openPrototype('api-compatibility.html')">
                    <div class="icon">🔗</div>
                    <h3>RN03对外API接口</h3>
                    <p>RN03服务对外提供的API接口说明和兼容性要求</p>
                    <div class="badge">API</div>
                </div>

                <div class="prototype-card" onclick="openPrototype('legacy-config.html')">
                    <div class="icon">⚙️</div>
                    <h3>原有配置对比</h3>
                    <p>展示原有RN03配置界面与新系统的对比</p>
                    <div class="badge">对比展示</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 宝信软件 - RtService团队</p>
            <p>插件式过滤系统界面原型 v1.0</p>
            <p style="margin-top: 10px; font-size: 0.9em; color: #999;">
                原型图基于表格+表单设计理念，保持简洁实用的管理界面风格
            </p>
        </div>
    </div>
    
    <script>
        function openPrototype(filename) {
            window.location.href = filename;
        }
        
        // 添加键盘导航支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                window.location.href = '../index.html';
            }
        });
        
        // 添加卡片点击效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.prototype-card');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
