<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件管理 - 原型图</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: white;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-header h1 {
            color: #303133;
            font-size: 24px;
            margin: 0;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #409EFF;
            color: white;
        }
        
        .btn-primary:hover {
            background: #337ecc;
        }
        
        .btn-default {
            background: #f4f4f5;
            color: #606266;
            border: 1px solid #dcdfe6;
        }
        
        .btn-default:hover {
            background: #ecf5ff;
            border-color: #409EFF;
            color: #409EFF;
        }
        
        .search-section {
            background: white;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .search-form {
            display: flex;
            gap: 20px;
            align-items: end;
            flex-wrap: wrap;
        }
        
        .form-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-item label {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
        }
        
        .form-input, .form-select {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            min-width: 180px;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #409EFF;
        }
        
        .table-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-header h3 {
            color: #303133;
            font-size: 16px;
            margin: 0;
        }
        
        .table-info {
            color: #909399;
            font-size: 14px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #909399;
            font-size: 14px;
            border-bottom: 1px solid #ebeef5;
        }
        
        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;
        }
        
        .data-table tr:hover {
            background: #f5f7fa;
        }
        
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-enabled {
            background: #f0f9ff;
            color: #67c23a;
            border: 1px solid #e1f3d8;
        }
        
        .status-disabled {
            background: #fdf6ec;
            color: #e6a23c;
            border: 1px solid #faecd8;
        }
        
        .status-error {
            background: #fef0f0;
            color: #f56c6c;
            border: 1px solid #fde2e2;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 3px;
        }
        
        .btn-info {
            background: #909399;
            color: white;
        }
        
        .btn-warning {
            background: #e6a23c;
            color: white;
        }
        
        .btn-success {
            background: #67c23a;
            color: white;
        }
        
        .btn-danger {
            background: #f56c6c;
            color: white;
        }
        
        .pagination {
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #ebeef5;
        }
        
        .pagination-info {
            color: #909399;
            font-size: 14px;
        }
        
        .pagination-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .back-link {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .back-link:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .search-form {
                flex-direction: column;
                gap: 15px;
            }
            
            .form-input, .form-select {
                min-width: auto;
            }
            
            .data-table {
                font-size: 12px;
            }
            
            .data-table th, .data-table td {
                padding: 8px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-link">← 返回原型首页</a>
        
        <!-- 页面头部 -->
        <div class="page-header">
            <h1>插件管理</h1>
            <div class="header-actions">
                <button class="btn btn-primary">📤 上传插件</button>
                <button class="btn btn-default">🔄 刷新</button>
            </div>
        </div>
        
        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-form">
                <div class="form-item">
                    <label>插件名称</label>
                    <input type="text" class="form-input" placeholder="请输入插件名称" value="">
                </div>
                <div class="form-item">
                    <label>插件状态</label>
                    <select class="form-select">
                        <option value="">全部状态</option>
                        <option value="ENABLED">启用</option>
                        <option value="DISABLED">禁用</option>
                        <option value="ERROR">错误</option>
                    </select>
                </div>
                <div class="form-item">
                    <label>插件作者</label>
                    <input type="text" class="form-input" placeholder="请输入作者" value="">
                </div>
                <div class="form-item">
                    <label>&nbsp;</label>
                    <div>
                        <button class="btn btn-primary">🔍 搜索</button>
                        <button class="btn btn-default">🔄 重置</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 表格区域 -->
        <div class="table-section">
            <div class="table-header">
                <h3>插件列表</h3>
                <div class="table-info">共 7 条记录</div>
            </div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>插件ID</th>
                        <th>插件名称</th>
                        <th>类型</th>
                        <th>版本</th>
                        <th>作者</th>
                        <th>超时(ms)</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>basic_switch_filter</td>
                        <td>基础开关过滤插件</td>
                        <td><span style="background: #e1f3d8; color: #529b2e; padding: 2px 6px; border-radius: 4px; font-size: 12px;">内置</span></td>
                        <td>1.0.0</td>
                        <td>系统管理员</td>
                        <td><span style="color: #909399; font-size: 12px;">500</span></td>
                        <td><span class="status-tag status-enabled">启用</span></td>
                        <td>2024-01-15 10:30:00</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-info btn-small">详情</button>
                                <button class="btn btn-primary btn-small">配置</button>
                                <button class="btn btn-warning btn-small">禁用</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>enhanced_time_filter</td>
                        <td>增强时间段过滤插件</td>
                        <td><span style="background: #e1f3d8; color: #529b2e; padding: 2px 6px; border-radius: 4px; font-size: 12px;">内置</span></td>
                        <td>1.2.0</td>
                        <td>系统管理员</td>
                        <td><span style="color: #909399; font-size: 12px;">1000</span></td>
                        <td><span class="status-tag status-enabled">启用</span></td>
                        <td>2024-01-15 10:35:00</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-info btn-small">详情</button>
                                <button class="btn btn-primary btn-small">配置</button>
                                <button class="btn btn-warning btn-small">禁用</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>repeat_rule_filter</td>
                        <td>重复规则过滤插件</td>
                        <td><span style="background: #e1f3d8; color: #529b2e; padding: 2px 6px; border-radius: 4px; font-size: 12px;">内置</span></td>
                        <td>1.1.0</td>
                        <td>系统管理员</td>
                        <td><span style="color: #909399; font-size: 12px;">800</span></td>
                        <td><span class="status-tag status-enabled">启用</span></td>
                        <td>2024-01-15 10:40:00</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-info btn-small">详情</button>
                                <button class="btn btn-primary btn-small">配置</button>
                                <button class="btn btn-warning btn-small">禁用</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>user_permission_filter</td>
                        <td>用户权限过滤插件</td>
                        <td><span style="background: #e1f3d8; color: #529b2e; padding: 2px 6px; border-radius: 4px; font-size: 12px;">内置</span></td>
                        <td>1.0.0</td>
                        <td>业务开发团队</td>
                        <td><span style="color: #909399; font-size: 12px;">2000</span></td>
                        <td><span class="status-tag status-enabled">启用</span></td>
                        <td>2024-01-16 09:15:00</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-info btn-small">详情</button>
                                <button class="btn btn-primary btn-small">配置</button>
                                <button class="btn btn-warning btn-small">禁用</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>frequency_limit_filter</td>
                        <td>频率限制过滤插件</td>
                        <td><span style="background: #e1f3d8; color: #529b2e; padding: 2px 6px; border-radius: 4px; font-size: 12px;">内置</span></td>
                        <td>1.0.0</td>
                        <td>系统管理员</td>
                        <td><span style="color: #909399; font-size: 12px;">1500</span></td>
                        <td><span class="status-tag status-enabled">启用</span></td>
                        <td>2024-01-17 16:30:00</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-info btn-small">详情</button>
                                <button class="btn btn-primary btn-small">配置</button>
                                <button class="btn btn-warning btn-small">禁用</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>

                    <!-- 自定义插件 -->
                    <tr>
                        <td>custom_oa_approval</td>
                        <td>OA审批过滤插件</td>
                        <td><span style="background: #fdf6ec; color: #b88230; padding: 2px 6px; border-radius: 4px; font-size: 12px;">自定义</span></td>
                        <td>1.0.0</td>
                        <td>业务团队</td>
                        <td><span style="color: #b88230; font-size: 12px;">5000</span></td>
                        <td><span class="status-tag status-enabled">启用</span></td>
                        <td>2024-01-18 09:15:00</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-info btn-small">详情</button>
                                <button class="btn btn-primary btn-small">配置</button>
                                <button class="btn btn-warning btn-small">禁用</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td>custom_business_rule</td>
                        <td>业务规则过滤插件</td>
                        <td><span style="background: #fdf6ec; color: #b88230; padding: 2px 6px; border-radius: 4px; font-size: 12px;">自定义</span></td>
                        <td>2.1.0</td>
                        <td>开发团队</td>
                        <td><span style="color: #b88230; font-size: 12px;">3000</span></td>
                        <td><span class="status-tag status-enabled">启用</span></td>
                        <td>2024-01-18 14:20:00</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-info btn-small">详情</button>
                                <button class="btn btn-primary btn-small">配置</button>
                                <button class="btn btn-warning btn-small">禁用</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>

                </tbody>
            </table>
            
            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示第 1-7 条记录，共 7 条
                </div>
                <div class="pagination-controls">
                    <select class="form-select" style="min-width: 80px;">
                        <option>10条/页</option>
                        <option>20条/页</option>
                        <option>50条/页</option>
                    </select>
                    <button class="btn btn-default btn-small">上一页</button>
                    <span style="padding: 0 10px; color: #409EFF;">1</span>
                    <button class="btn btn-default btn-small">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 弹窗组件 -->
    <div id="modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">标题</h3>
                <span class="modal-close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body" id="modal-body">
                内容
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" id="modal-confirm">确定</button>
            </div>
        </div>
    </div>

    <style>
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 90vh;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #303133;
        }

        .modal-close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #909399;
        }

        .modal-close:hover {
            color: #f56c6c;
        }

        .modal-body {
            padding: 30px;
            overflow-y: auto;
            flex: 1;
            min-height: 0;
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid #ebeef5;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
    </style>

    <script>
        // 弹窗功能
        function showModal(title, content, onConfirm) {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-body').innerHTML = content;
            document.getElementById('modal').style.display = 'block';

            const confirmBtn = document.getElementById('modal-confirm');
            confirmBtn.onclick = function() {
                if (onConfirm) onConfirm();
                closeModal();
            };
        }

        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 切换插件实现方式
        function toggleImplementationType() {
            const implementationType = document.getElementById('implementationType').value;
            const pluginMethod = document.getElementById('pluginMethod');
            const implementationHint = document.getElementById('implementationHint');
            const classHint = document.getElementById('classHint');
            const methodHint = document.getElementById('methodHint');

            if (implementationType === 'standard') {
                // 标准接口实现
                pluginMethod.value = 'execute';
                pluginMethod.readOnly = true;
                pluginMethod.style.backgroundColor = '#f5f5f5';

                implementationHint.textContent = '推荐使用标准FilterRulePlugin接口，提供更好的类型安全和性能';
                implementationHint.style.color = '#67c23a';

                classHint.textContent = '请输入实现FilterRulePlugin接口的类全限定名';
                methodHint.textContent = '标准接口固定为execute方法';

            } else {
                // 自定义方法实现
                pluginMethod.value = '';
                pluginMethod.readOnly = false;
                pluginMethod.style.backgroundColor = '#ffffff';
                pluginMethod.placeholder = '如: checkPermission';

                implementationHint.textContent = '兼容任意类和方法，通过反射适配器调用，性能略低';
                implementationHint.style.color = '#e6a23c';

                classHint.textContent = '可以是任意类，无需实现特定接口';
                methodHint.textContent = '可以是任意方法名，支持多种参数和返回类型';
            }
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('modal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 模拟交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 按钮点击效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                    
                    // 模拟操作提示
                    if (this.textContent.includes('上传插件')) {
                        showModal('上传自定义插件', `
                            <form style="max-width: 500px;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold; width: 120px;">插件ID *</td>
                                        <td style="padding: 8px;">
                                            <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" placeholder="如: custom_my_filter">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">插件名称 *</td>
                                        <td style="padding: 8px;">
                                            <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" placeholder="如: 我的自定义过滤插件">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">实现方式 *</td>
                                        <td style="padding: 8px;">
                                            <select id="implementationType" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" onchange="toggleImplementationType()">
                                                <option value="standard">标准接口实现（推荐）</option>
                                                <option value="custom">自定义方法实现（兼容性）</option>
                                            </select>
                                            <div style="font-size: 12px; color: #909399; margin-top: 5px;">
                                                <span id="implementationHint">推荐使用标准FilterRulePlugin接口，提供更好的类型安全和性能</span>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">类全限定名 *</td>
                                        <td style="padding: 8px;">
                                            <input type="text" id="pluginClass" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" placeholder="如: com.company.filter.MyCustomFilter">
                                            <div style="font-size: 12px; color: #909399; margin-top: 5px;">
                                                <span id="classHint">请输入实现FilterRulePlugin接口的类全限定名</span>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">执行方法名 *</td>
                                        <td style="padding: 8px;">
                                            <input type="text" id="pluginMethod" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" placeholder="如: executeFilter" value="execute">
                                            <div style="font-size: 12px; color: #909399; margin-top: 5px;">
                                                <span id="methodHint">标准接口固定为execute方法</span>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">插件版本</td>
                                        <td style="padding: 8px;">
                                            <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="1.0.0">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">插件描述</td>
                                        <td style="padding: 8px;">
                                            <textarea style="width: 100%; height: 60px; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" placeholder="请输入插件功能描述"></textarea>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">JAR文件 *</td>
                                        <td style="padding: 8px;">
                                            <input type="file" accept=".jar" style="width: 100%;">
                                            <div style="font-size: 12px; color: #909399; margin-top: 5px;">支持 .jar 格式的插件文件</div>
                                        </td>
                                    </tr>
                                </table>
                            </form>
                        `, function() {
                            alert('自定义插件上传成功！');
                        });
                    } else if (this.textContent.includes('详情')) {
                        // 根据点击的行获取插件信息
                        const row = this.closest('tr');
                        const pluginId = row.cells[0].textContent;
                        const pluginName = row.cells[1].textContent;
                        const pluginType = row.cells[2].textContent.trim();
                        const version = row.cells[3].textContent;
                        const author = row.cells[4].textContent;

                        // 根据插件类型显示不同的详情
                        let detailContent = '';
                        if (pluginType === '内置') {
                            // 内置插件详情
                            const classNames = {
                                'basic_switch_filter': 'com.baosight.rtservice.filter.plugins.BasicSwitchPlugin',
                                'enhanced_time_filter': 'com.baosight.rtservice.filter.plugins.TimeFilterPlugin',
                                'repeat_rule_filter': 'com.baosight.rtservice.filter.plugins.RepeatRulePlugin',
                                'user_permission_filter': 'com.baosight.rtservice.filter.plugins.UserPermissionPlugin',
                                'frequency_limit_filter': 'com.baosight.rtservice.filter.plugins.FrequencyLimitPlugin'
                            };

                            detailContent = `
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold; width: 120px;">插件ID:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${pluginId}</td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">插件名称:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${pluginName}</td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">插件类型:</td><td style="padding: 8px; border-bottom: 1px solid #eee;"><span style="background: #e1f3d8; color: #529b2e; padding: 2px 6px; border-radius: 4px; font-size: 12px;">内置插件</span></td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">实现方式:</td><td style="padding: 8px; border-bottom: 1px solid #eee;"><span style="background: #e1f3d8; color: #529b2e; padding: 2px 6px; border-radius: 4px; font-size: 12px;">标准接口实现</span></td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">类全限定名:</td><td style="padding: 8px; border-bottom: 1px solid #eee; font-family: monospace; font-size: 12px;">${classNames[pluginId] || 'N/A'}</td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">执行方法名:</td><td style="padding: 8px; border-bottom: 1px solid #eee; font-family: monospace; color: #409EFF;">execute()</td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">版本:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${version}</td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">作者:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${author}</td></tr>
                                    <tr><td style="padding: 8px; font-weight: bold;">描述:</td><td style="padding: 8px;">系统内置插件，使用@Plugin注解和FilterRulePlugin接口</td></tr>
                                </table>
                            `;
                        } else {
                            // 自定义插件详情
                            const customDetails = {
                                'custom_oa_approval': {
                                    className: 'com.company.business.filter.OAApprovalFilterPlugin',
                                    method: 'execute',
                                    implementationType: 'standard',
                                    description: '标准接口实现的OA审批插件，实现FilterRulePlugin接口'
                                },
                                'custom_business_rule': {
                                    className: 'com.company.business.filter.LegacyBusinessChecker',
                                    method: 'validateBusinessRule',
                                    implementationType: 'custom',
                                    description: '兼容遗留系统的业务规则插件，通过反射适配器调用'
                                }
                            };

                            const detail = customDetails[pluginId] || {
                                className: 'N/A',
                                method: 'N/A',
                                implementationType: 'custom',
                                description: '自定义插件'
                            };

                            const implementationTypeDisplay = detail.implementationType === 'standard'
                                ? '<span style="background: #e1f3d8; color: #529b2e; padding: 2px 6px; border-radius: 4px; font-size: 12px;">标准接口实现</span>'
                                : '<span style="background: #fdf6ec; color: #b88230; padding: 2px 6px; border-radius: 4px; font-size: 12px;">自定义方法实现</span>';

                            detailContent = `
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold; width: 120px;">插件ID:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${pluginId}</td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">插件名称:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${pluginName}</td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">插件类型:</td><td style="padding: 8px; border-bottom: 1px solid #eee;"><span style="background: #fdf6ec; color: #b88230; padding: 2px 6px; border-radius: 4px; font-size: 12px;">自定义插件</span></td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">实现方式:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${implementationTypeDisplay}</td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">类全限定名:</td><td style="padding: 8px; border-bottom: 1px solid #eee; font-family: monospace; font-size: 11px; word-break: break-all;">${detail.className}</td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">执行方法名:</td><td style="padding: 8px; border-bottom: 1px solid #eee; font-family: monospace; color: #409EFF;">${detail.method}()</td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">版本:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${version}</td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">作者:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${author}</td></tr>
                                    <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">JAR路径:</td><td style="padding: 8px; border-bottom: 1px solid #eee; font-family: monospace; font-size: 12px;">/plugins/${pluginId}.jar</td></tr>
                                    <tr><td style="padding: 8px; font-weight: bold;">描述:</td><td style="padding: 8px;">${detail.description}</td></tr>
                                </table>
                            `;
                        }

                        showModal('插件详情', detailContent);
                    } else if (this.textContent.includes('配置')) {
                        // 根据点击的行获取插件信息
                        const row = this.closest('tr');
                        const pluginId = row.cells[0].textContent;
                        const pluginName = row.cells[1].textContent;
                        const pluginType = row.cells[2].textContent.trim();

                        if (pluginType === '内置') {
                            // 内置插件只显示说明，不允许配置
                            showModal('内置插件说明', `
                                <div style="text-align: center; padding: 20px;">
                                    <div style="font-size: 48px; color: #909399; margin-bottom: 15px;">ℹ️</div>
                                    <h4 style="color: #303133; margin-bottom: 10px;">内置插件无需配置</h4>
                                    <p style="color: #606266; font-size: 14px; margin-bottom: 20px;">
                                        内置插件由系统自动管理，无需手动配置加载顺序和超时时间
                                    </p>
                                    <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; text-align: left;">
                                        <div style="font-size: 12px; color: #606266;">
                                            <div><strong>插件名称:</strong> ${pluginName}</div>
                                            <div style="margin-top: 5px;"><strong>插件ID:</strong> ${pluginId}</div>
                                            <div style="margin-top: 5px;"><strong>配置方式:</strong> 在"规则管理"中创建规则时配置业务参数</div>
                                            <div style="margin-top: 10px; color: #409EFF;"><strong>💡 提示:</strong> 内置插件的技术参数由系统优化，业务参数在创建规则时配置</div>
                                        </div>
                                    </div>
                                </div>
                            `);
                        } else {
                            // 自定义插件才允许配置
                            showModal('自定义插件配置', `
                                <div>
                                    <h4 style="margin-bottom: 15px;">配置自定义插件: ${pluginName}</h4>
                                    <table style="width: 100%; border-collapse: collapse;">
                                        <tr>
                                            <td style="padding: 8px; font-weight: bold; width: 120px;">插件ID:</td>
                                            <td style="padding: 8px;">
                                                <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px; background: #f5f5f5;" value="${pluginId}" readonly>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 8px; font-weight: bold;">插件名称:</td>
                                            <td style="padding: 8px;">
                                                <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="${pluginName}">
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 8px; font-weight: bold;">JAR文件路径:</td>
                                            <td style="padding: 8px;">
                                                <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="/plugins/${pluginId}.jar">
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 8px; font-weight: bold;">超时时间(ms):</td>
                                            <td style="padding: 8px;">
                                                <input type="number" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="5000" min="1000" max="30000">
                                                <div style="font-size: 12px; color: #909399; margin-top: 5px;">自定义插件可能需要调用外部服务，建议设置较长超时时间</div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 8px; font-weight: bold;">插件描述:</td>
                                            <td style="padding: 8px;">
                                                <textarea style="width: 100%; height: 60px; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" placeholder="请输入插件功能描述">自定义业务插件</textarea>
                                            </td>
                                        </tr>
                                    </table>

                                    <div style="margin-top: 20px; padding: 15px; background: #fdf6ec; border-radius: 4px;">
                                        <h5 style="color: #b88230; margin-bottom: 10px;">自定义插件配置说明:</h5>
                                        <div style="font-size: 12px; color: #b88230;">
                                            <div>• JAR文件路径：插件实现文件的存储位置</div>
                                            <div>• 超时时间：插件执行的最大等待时间，建议根据实际业务调整</div>
                                            <div>• 插件描述：用于系统文档和管理界面显示</div>
                                        </div>
                                    </div>
                                </div>
                            `, function() {
                                alert('自定义插件配置已保存！');
                            });
                        }
                    } else if (this.textContent.includes('搜索')) {
                        alert('模拟：执行搜索操作');
                    }
                });
            });
            
            // 表格行悬停效果
            const rows = document.querySelectorAll('.data-table tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f5f7fa';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });
        });
    </script>
</body>
</html>
