<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原有配置对比 - 原型图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: white;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .page-header h1 {
            color: #303133;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .page-header p {
            color: #909399;
            font-size: 14px;
        }
        
        .comparison-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .config-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section-header.legacy {
            background: #fdf6ec;
            border-bottom-color: #f5dab1;
        }
        
        .section-header.new {
            background: #e1f3d8;
            border-bottom-color: #c2e7b0;
        }
        
        .section-header h3 {
            color: #303133;
            font-size: 16px;
            margin: 0;
        }
        
        .version-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .tag-legacy {
            background: #fdf6ec;
            color: #b88230;
            border: 1px solid #f5dab1;
        }
        
        .tag-new {
            background: #e1f3d8;
            color: #529b2e;
            border: 1px solid #c2e7b0;
        }
        
        .section-content {
            padding: 30px;
        }
        
        .config-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .config-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #909399;
            font-size: 14px;
            border-bottom: 1px solid #ebeef5;
        }
        
        .config-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;
        }
        
        .config-table tr:hover {
            background: #f5f7fa;
        }
        
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-enabled {
            background: #f0f9ff;
            color: #67c23a;
            border: 1px solid #e1f3d8;
        }
        
        .status-disabled {
            background: #fdf6ec;
            color: #e6a23c;
            border: 1px solid #faecd8;
        }
        
        .status-migrated {
            background: #e1f3d8;
            color: #529b2e;
            border: 1px solid #c2e7b0;
        }
        
        .feature-comparison {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .comparison-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            background: #fafafa;
        }
        
        .comparison-header h3 {
            color: #303133;
            font-size: 16px;
            margin: 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .comparison-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #909399;
            font-size: 14px;
            border-bottom: 1px solid #ebeef5;
        }
        
        .comparison-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;
            vertical-align: top;
        }
        
        .comparison-table tr:hover {
            background: #f5f7fa;
        }
        
        .feature-name {
            font-weight: 500;
            color: #303133;
        }
        
        .support-yes {
            color: #67c23a;
            font-weight: 500;
        }
        
        .support-no {
            color: #f56c6c;
            font-weight: 500;
        }
        
        .support-partial {
            color: #e6a23c;
            font-weight: 500;
        }
        
        .migration-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .migration-steps {
            padding: 30px;
        }
        
        .step-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .step-number {
            background: #409EFF;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .step-content h4 {
            color: #303133;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .step-content p {
            color: #606266;
            font-size: 13px;
            line-height: 1.5;
        }
        
        .back-link {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .back-link:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }
        
        @media (max-width: 1200px) {
            .comparison-layout {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-link">← 返回原型首页</a>
        
        <!-- 页面头部 -->
        <div class="page-header">
            <h1>原有配置对比</h1>
            <p>展示RN03原有勿扰配置与新插件式过滤系统的对比</p>
        </div>
        
        <!-- 配置对比 -->
        <div class="comparison-layout">
            <!-- 原有配置 -->
            <div class="config-section">
                <div class="section-header legacy">
                    <h3>原有RN03勿扰配置</h3>
                    <span class="version-tag tag-legacy">当前版本</span>
                </div>
                <div class="section-content">
                    <table class="config-table">
                        <thead>
                            <tr>
                                <th>消息标识</th>
                                <th>弹窗类型</th>
                                <th>勿扰状态</th>
                                <th>时间段</th>
                                <th>重复类型</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>SYSTEM_ALERT</td>
                                <td>弹窗</td>
                                <td><span class="status-tag status-enabled">开启</span></td>
                                <td>09:00-18:00</td>
                                <td>工作日</td>
                            </tr>
                            <tr>
                                <td>BUSINESS_NOTIFY</td>
                                <td>通知</td>
                                <td><span class="status-tag status-enabled">开启</span></td>
                                <td>08:00-20:00</td>
                                <td>每天</td>
                            </tr>
                            <tr>
                                <td>USER_ACTION</td>
                                <td>弹窗</td>
                                <td><span class="status-tag status-disabled">关闭</span></td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>MAINTENANCE</td>
                                <td>通知</td>
                                <td><span class="status-tag status-enabled">开启</span></td>
                                <td>全天</td>
                                <td>节假日</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 新系统配置 -->
            <div class="config-section">
                <div class="section-header new">
                    <h3>新插件式过滤系统</h3>
                    <span class="version-tag tag-new">升级版本</span>
                </div>
                <div class="section-content">
                    <table class="config-table">
                        <thead>
                            <tr>
                                <th>过滤器组</th>
                                <th>执行方式</th>
                                <th>规则数量</th>
                                <th>状态</th>
                                <th>迁移状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>系统告警过滤组</td>
                                <td>责任链</td>
                                <td>3</td>
                                <td><span class="status-tag status-enabled">启用</span></td>
                                <td><span class="status-tag status-migrated">已迁移</span></td>
                            </tr>
                            <tr>
                                <td>业务通知过滤组</td>
                                <td>逻辑运算</td>
                                <td>4</td>
                                <td><span class="status-tag status-enabled">启用</span></td>
                                <td><span class="status-tag status-migrated">已迁移</span></td>
                            </tr>
                            <tr>
                                <td>用户操作提醒组</td>
                                <td>责任链</td>
                                <td>2</td>
                                <td><span class="status-tag status-enabled">启用</span></td>
                                <td><span class="status-tag status-migrated">已迁移</span></td>
                            </tr>
                            <tr>
                                <td>维护窗口过滤组</td>
                                <td>责任链</td>
                                <td>2</td>
                                <td><span class="status-tag status-enabled">启用</span></td>
                                <td><span class="status-tag status-migrated">已迁移</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- 功能对比 -->
        <div class="feature-comparison">
            <div class="comparison-header">
                <h3>功能特性对比</h3>
            </div>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>功能特性</th>
                        <th>原有系统</th>
                        <th>新系统</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="feature-name">基础开关控制</td>
                        <td class="support-yes">✓ 支持</td>
                        <td class="support-yes">✓ 支持</td>
                        <td>完全兼容，功能增强</td>
                    </tr>
                    <tr>
                        <td class="feature-name">时间段过滤</td>
                        <td class="support-partial">△ 部分支持</td>
                        <td class="support-yes">✓ 完全支持</td>
                        <td>新增跨天时间段支持</td>
                    </tr>
                    <tr>
                        <td class="feature-name">重复规则</td>
                        <td class="support-partial">△ 部分支持</td>
                        <td class="support-yes">✓ 完全支持</td>
                        <td>新增节假日支持</td>
                    </tr>
                    <tr>
                        <td class="feature-name">用户权限过滤</td>
                        <td class="support-no">✗ 不支持</td>
                        <td class="support-yes">✓ 支持</td>
                        <td>新增功能</td>
                    </tr>
                    <tr>
                        <td class="feature-name">自定义插件</td>
                        <td class="support-no">✗ 不支持</td>
                        <td class="support-yes">✓ 支持</td>
                        <td>支持业务方自定义开发</td>
                    </tr>
                    <tr>
                        <td class="feature-name">复杂规则组合</td>
                        <td class="support-no">✗ 不支持</td>
                        <td class="support-yes">✓ 支持</td>
                        <td>支持责任链和逻辑运算</td>
                    </tr>
                    <tr>
                        <td class="feature-name">执行统计监控</td>
                        <td class="support-no">✗ 不支持</td>
                        <td class="support-yes">✓ 支持</td>
                        <td>完整的监控统计功能</td>
                    </tr>
                    <tr>
                        <td class="feature-name">配置管理界面</td>
                        <td class="support-partial">△ 简单</td>
                        <td class="support-yes">✓ 完善</td>
                        <td>表格化管理界面</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 迁移步骤 -->
        <div class="migration-section">
            <div class="comparison-header">
                <h3>迁移实施步骤</h3>
            </div>
            <div class="migration-steps">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>数据备份与分析</h4>
                        <p>备份现有rs_rn_config表数据，分析现有配置规则，制定迁移映射关系</p>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>新系统部署</h4>
                        <p>部署插件式过滤系统，创建新的数据库表结构，部署内置插件</p>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>数据迁移</h4>
                        <p>执行数据迁移脚本，将现有配置转换为新的过滤器组和规则关联</p>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4>功能验证</h4>
                        <p>在测试环境验证迁移后的功能，确保与原有功能完全兼容</p>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h4>灰度发布</h4>
                        <p>在生产环境进行灰度发布，逐步切换到新系统，监控运行状态</p>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">6</div>
                    <div class="step-content">
                        <h4>用户培训</h4>
                        <p>对管理员进行新系统培训，提供操作手册和技术支持</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 添加表格行悬停效果
        document.addEventListener('DOMContentLoaded', function() {
            const tables = document.querySelectorAll('.config-table, .comparison-table');
            tables.forEach(table => {
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    row.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#f5f7fa';
                    });
                    row.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = '';
                    });
                });
            });
        });
    </script>
</body>
</html>
