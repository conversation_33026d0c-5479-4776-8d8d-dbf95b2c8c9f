<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API兼容性说明 - 原型图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .page-header h1 {
            color: #303133;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .page-header p {
            color: #909399;
            font-size: 16px;
        }
        
        .api-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .section-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            background: #fafafa;
        }
        
        .section-header h3 {
            color: #303133;
            font-size: 18px;
            margin: 0;
        }
        
        .section-content {
            padding: 30px;
        }
        
        .api-flow {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
            margin: 30px 0;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            gap: 20px;
            width: 100%;
            justify-content: center;
        }
        
        .api-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            min-width: 200px;
            transition: all 0.3s ease;
        }
        
        .api-box:hover {
            border-color: #409EFF;
            transform: translateY(-2px);
        }
        
        .api-box.current {
            border-color: #67c23a;
            background: #f0f9ff;
        }
        
        .api-box.new {
            border-color: #409EFF;
            background: #ecf5ff;
        }
        
        .api-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
        }
        
        .api-desc {
            font-size: 14px;
            color: #606266;
            margin-bottom: 10px;
        }
        
        .api-method {
            font-size: 12px;
            color: #909399;
            font-family: 'Monaco', 'Consolas', monospace;
            background: white;
            padding: 5px 8px;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
        }
        
        .arrow {
            font-size: 24px;
            color: #409EFF;
            margin: 10px 0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.5;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .comparison-table th {
            background: #fafafa;
            padding: 15px;
            text-align: left;
            font-weight: 500;
            color: #909399;
            border-bottom: 2px solid #ebeef5;
        }
        
        .comparison-table td {
            padding: 15px;
            border-bottom: 1px solid #ebeef5;
            vertical-align: top;
        }
        
        .comparison-table tr:hover {
            background: #f5f7fa;
        }
        
        .highlight-box {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .highlight-title {
            font-weight: bold;
            color: #d46b08;
            margin-bottom: 8px;
        }
        
        .highlight-content {
            color: #8c4a00;
            font-size: 14px;
        }
        
        .back-link {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .back-link:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .flow-step {
                flex-direction: column;
            }
            
            .api-box {
                min-width: auto;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-link">← 返回原型首页</a>
        
        <!-- 页面头部 -->
        <div class="page-header">
            <h1>RN03对外API接口说明</h1>
            <p>新插件式过滤系统需要完全兼容的对外API接口</p>
        </div>
        
        <!-- API调用流程 -->
        <div class="api-section">
            <div class="section-header">
                <h3>🔄 API调用流程对比</h3>
            </div>
            <div class="section-content">
                <div class="api-flow">
                    <div class="flow-step">
                        <div class="api-box current">
                            <div class="api-title">业务服务</div>
                            <div class="api-desc">发起弹窗/通知请求</div>
                            <div class="api-method">ServiceRNNF01/ServiceRNWS01</div>
                        </div>
                    </div>
                    
                    <div class="arrow">↓</div>
                    
                    <div class="flow-step">
                        <div class="api-box current">
                            <div class="api-title">Dialog.filter()</div>
                            <div class="api-desc">统一过滤入口</div>
                            <div class="api-method">dialog.filter(dialogType)</div>
                        </div>
                    </div>
                    
                    <div class="arrow">↓</div>
                    
                    <div class="flow-step">
                        <div class="api-box current">
                            <div class="api-title">RN03.messageFilter()</div>
                            <div class="api-desc">过滤逻辑实现</div>
                            <div class="api-method">ServiceRN03.messageFilter(inInfo)</div>
                        </div>
                        <div style="margin: 0 20px; color: #409EFF; font-size: 20px;">→</div>
                        <div class="api-box new">
                            <div class="api-title">新过滤引擎</div>
                            <div class="api-desc">插件式过滤系统</div>
                            <div class="api-method">FilterEngine.execute()</div>
                        </div>
                    </div>
                    
                    <div class="arrow">↓</div>
                    
                    <div class="flow-step">
                        <div class="api-box current">
                            <div class="api-title">返回结果</div>
                            <div class="api-desc">isFilter: boolean</div>
                            <div class="api-method">EiInfo.set("isFilter", result)</div>
                        </div>
                    </div>
                </div>
                
                <div class="highlight-box">
                    <div class="highlight-title">🔑 关键兼容点</div>
                    <div class="highlight-content">
                        新系统只需要重新实现 <code>ServiceRN03.messageFilter()</code> 方法，
                        其他所有API接口保持完全不变，确保业务方无感知升级。
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 对外API接口列表 -->
        <div class="api-section">
            <div class="section-header">
                <h3>📋 RN03对外API接口列表</h3>
            </div>
            <div class="section-content">
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>接口名称</th>
                            <th>方法签名</th>
                            <th>功能说明</th>
                            <th>调用方</th>
                            <th>兼容性要求</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>消息过滤器</strong></td>
                            <td><code>messageFilter(EiInfo inInfo)</code></td>
                            <td>核心过滤逻辑，判断消息是否应被过滤</td>
                            <td>Dialog.filter()</td>
                            <td>✅ 必须完全兼容</td>
                        </tr>
                        <tr>
                            <td><strong>通过ID查询状态</strong></td>
                            <td><code>queryStatusById(EiInfo inInfo)</code></td>
                            <td>根据消息标识查询勿扰模式配置状态</td>
                            <td>业务系统</td>
                            <td>✅ 必须完全兼容</td>
                        </tr>
                        <tr>
                            <td><strong>更新状态</strong></td>
                            <td><code>updateStatus(EiInfo inInfo)</code></td>
                            <td>更新通知/弹窗勿扰模式设置</td>
                            <td>管理界面</td>
                            <td>✅ 必须完全兼容</td>
                        </tr>
                        <tr>
                            <td><strong>查询状态</strong></td>
                            <td><code>queryStatus(EiInfo inInfo)</code></td>
                            <td>查询勿扰模式配置列表</td>
                            <td>管理界面</td>
                            <td>✅ 必须完全兼容</td>
                        </tr>
                        <tr>
                            <td><strong>基础CRUD</strong></td>
                            <td><code>query/insert/update/delete</code></td>
                            <td>基础的增删改查操作</td>
                            <td>管理界面</td>
                            <td>✅ 保持兼容</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 实现示例 -->
        <div class="api-section">
            <div class="section-header">
                <h3>💻 新系统实现示例</h3>
            </div>
            <div class="section-content">
                <p style="margin-bottom: 15px; color: #606266;">新系统中的 ServiceRN03.messageFilter() 实现：</p>
                <div class="code-block">
@Service("RN03")
public class ServiceRN03 extends ServiceBase {
    
    @Autowired
    private FilterEngine filterEngine;
    
    /**
     * 消息过滤器 - 保持API完全兼容
     */
    public EiInfo messageFilter(EiInfo inInfo) throws ParseException {
        EiInfo outInfo = new EiInfo();
        
        try {
            // 1. 提取参数（与原有方式一致）
            String messageCode = (String) inInfo.get("messageCode");
            String dialogType = (String) inInfo.get("dialogType");
            
            // 2. 构建过滤上下文
            FilterContext context = FilterContext.builder()
                .messageCode(messageCode)
                .dialogType(dialogType)
                .timestamp(System.currentTimeMillis())
                .build();
            
            // 3. 调用新的过滤引擎
            FilterResult result = filterEngine.execute(context);
            
            // 4. 返回格式保持一致
            outInfo.set("isFilter", result.isDeny());
            outInfo.setStatus(0);
            outInfo.setMsg("SUCCESS");
            
        } catch (FilterException e) {
            // 5. 异常处理保持兼容
            log.error("过滤器执行异常", e);
            outInfo.set("isFilter", false); // 异常时默认不过滤
            outInfo.setStatus(-1);
            outInfo.setMsg(e.getMessage());
        }
        
        return outInfo;
    }
}
                </div>
                
                <div class="highlight-box">
                    <div class="highlight-title">🎯 兼容性保证</div>
                    <div class="highlight-content">
                        1. <strong>方法签名不变</strong>：完全保持原有方法签名<br>
                        2. <strong>参数格式不变</strong>：输入输出格式完全一致<br>
                        3. <strong>调用方式不变</strong>：业务方调用代码无需修改<br>
                        4. <strong>异常处理兼容</strong>：异常时的降级策略保持一致<br>
                        5. <strong>性能要求</strong>：响应时间不超过原有系统
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 迁移策略 -->
        <div class="api-section">
            <div class="section-header">
                <h3>🚀 迁移策略</h3>
            </div>
            <div class="section-content">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div style="background: #f8f9fa; border-radius: 6px; padding: 20px;">
                        <h4 style="color: #303133; margin-bottom: 10px;">阶段一：并行部署</h4>
                        <ul style="color: #606266; font-size: 14px; line-height: 1.6;">
                            <li>新系统与原系统并行运行</li>
                            <li>通过配置开关控制流量分配</li>
                            <li>实时对比两套系统的执行结果</li>
                            <li>确保结果一致性</li>
                        </ul>
                    </div>
                    
                    <div style="background: #f8f9fa; border-radius: 6px; padding: 20px;">
                        <h4 style="color: #303133; margin-bottom: 10px;">阶段二：灰度切换</h4>
                        <ul style="color: #606266; font-size: 14px; line-height: 1.6;">
                            <li>按消息类型逐步切换</li>
                            <li>从低频消息开始验证</li>
                            <li>监控系统性能和稳定性</li>
                            <li>出现问题可快速回滚</li>
                        </ul>
                    </div>
                    
                    <div style="background: #f8f9fa; border-radius: 6px; padding: 20px;">
                        <h4 style="color: #303133; margin-bottom: 10px;">阶段三：全量切换</h4>
                        <ul style="color: #606266; font-size: 14px; line-height: 1.6;">
                            <li>所有流量切换到新系统</li>
                            <li>保留原系统作为备份</li>
                            <li>持续监控运行状态</li>
                            <li>稳定运行后下线原系统</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
