<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>勿扰模式配置 - 原型图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: white;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-header h1 {
            color: #303133;
            font-size: 24px;
            margin: 0;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #409EFF;
            color: white;
        }
        
        .btn-primary:hover {
            background: #337ecc;
        }
        
        .btn-default {
            background: #f4f4f5;
            color: #606266;
            border: 1px solid #dcdfe6;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .btn-success {
            background: #67c23a;
            color: white;
        }
        
        .btn-warning {
            background: #e6a23c;
            color: white;
        }
        
        .btn-danger {
            background: #f56c6c;
            color: white;
        }
        
        .search-section {
            background: white;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .search-form {
            display: flex;
            gap: 20px;
            align-items: end;
            flex-wrap: wrap;
        }
        
        .form-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-item label {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
        }
        
        .form-input, .form-select {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            min-width: 180px;
        }
        
        .table-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-header h3 {
            color: #303133;
            font-size: 16px;
            margin: 0;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #909399;
            font-size: 14px;
            border-bottom: 1px solid #ebeef5;
        }
        
        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;
        }
        
        .data-table tr:hover {
            background: #f5f7fa;
        }
        
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-enabled {
            background: #f0f9ff;
            color: #67c23a;
            border: 1px solid #e1f3d8;
        }
        
        .status-disabled {
            background: #fdf6ec;
            color: #e6a23c;
            border: 1px solid #faecd8;
        }
        
        .execution-type-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .type-chain {
            background: #e1f3d8;
            color: #529b2e;
            border: 1px solid #c2e7b0;
        }
        
        .type-logic {
            background: #fdf6ec;
            color: #b88230;
            border: 1px solid #f5dab1;
        }
        
        .rule-count {
            background: #ecf5ff;
            color: #409eff;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        
        .back-link {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .back-link:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .search-form {
                flex-direction: column;
                gap: 15px;
            }
            
            .form-input, .form-select {
                min-width: auto;
            }
            
            .data-table {
                font-size: 12px;
            }
            
            .data-table th, .data-table td {
                padding: 8px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-link">← 返回原型首页</a>
        
        <!-- 页面头部 -->
        <div class="page-header">
            <h1>勿扰模式配置</h1>
            <div style="font-size: 14px; color: #606266; margin-bottom: 10px;">
                配置不同消息类型的过滤规则，实现智能勿扰功能
            </div>
            <div>
                <button class="btn btn-primary">➕ 创建勿扰配置</button>
            </div>
        </div>
        
        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-form">
                <div class="form-item">
                    <label>过滤器组名称</label>
                    <input type="text" class="form-input" placeholder="请输入过滤器组名称" value="">
                </div>
                <div class="form-item">
                    <label>消息标识</label>
                    <input type="text" class="form-input" placeholder="请输入消息标识" value="">
                </div>
                <div class="form-item">
                    <label>弹窗类型</label>
                    <select class="form-select">
                        <option value="">全部类型</option>
                        <option value="NOTIFICATION">通知</option>
                        <option value="POPUP_WINDOW">弹窗</option>
                    </select>
                </div>
                <div class="form-item">
                    <label>执行方式</label>
                    <select class="form-select">
                        <option value="">全部方式</option>
                        <option value="CHAIN">责任链</option>
                        <option value="LOGIC">逻辑运算</option>
                    </select>
                </div>
                <div class="form-item">
                    <label>&nbsp;</label>
                    <div>
                        <button class="btn btn-primary">🔍 搜索</button>
                        <button class="btn btn-default">🔄 重置</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 表格区域 -->
        <div class="table-section">
            <div class="table-header">
                <h3>勿扰配置列表</h3>
                <div style="color: #909399; font-size: 14px;">共 5 条记录</div>
            </div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>勿扰配置名称</th>
                        <th>消息标识</th>
                        <th>弹窗类型</th>
                        <th>执行方式</th>
                        <th>过滤规则数</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>系统告警勿扰配置</td>
                        <td>SYSTEM_ALERT</td>
                        <td>弹窗</td>
                        <td><span class="execution-type-tag type-chain">责任链</span></td>
                        <td><span class="rule-count">3</span></td>
                        <td><span class="status-tag status-enabled">启用</span></td>
                        <td>2024-01-15 10:30</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-small">编辑</button>
                                <button class="btn btn-success btn-small">规则配置</button>
                                <button class="btn btn-default btn-small">测试</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>业务通知勿扰配置</td>
                        <td>BUSINESS_NOTIFY</td>
                        <td>通知</td>
                        <td><span class="execution-type-tag type-logic">逻辑运算</span></td>
                        <td><span class="rule-count">4</span></td>
                        <td><span class="status-tag status-enabled">启用</span></td>
                        <td>2024-01-15 11:15</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-small">编辑</button>
                                <button class="btn btn-success btn-small">规则配置</button>
                                <button class="btn btn-default btn-small">测试</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>用户操作提醒组</td>
                        <td>USER_ACTION</td>
                        <td>弹窗</td>
                        <td><span class="execution-type-tag type-chain">责任链</span></td>
                        <td><span class="rule-count">2</span></td>
                        <td><span class="status-tag status-enabled">启用</span></td>
                        <td>2024-01-16 09:20</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-small">编辑</button>
                                <button class="btn btn-success btn-small">规则配置</button>
                                <button class="btn btn-default btn-small">测试</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>维护窗口过滤组</td>
                        <td>MAINTENANCE</td>
                        <td>通知</td>
                        <td><span class="execution-type-tag type-chain">责任链</span></td>
                        <td><span class="rule-count">1</span></td>
                        <td><span class="status-tag status-disabled">禁用</span></td>
                        <td>2024-01-16 14:45</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-small">编辑</button>
                                <button class="btn btn-success btn-small">规则配置</button>
                                <button class="btn btn-default btn-small">测试</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>紧急事件过滤组</td>
                        <td>EMERGENCY</td>
                        <td>弹窗</td>
                        <td><span class="execution-type-tag type-logic">逻辑运算</span></td>
                        <td><span class="rule-count">5</span></td>
                        <td><span class="status-tag status-enabled">启用</span></td>
                        <td>2024-01-17 16:30</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-small">编辑</button>
                                <button class="btn btn-success btn-small">规则配置</button>
                                <button class="btn btn-default btn-small">测试</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <!-- 分页 -->
            <div style="padding: 20px 30px; display: flex; justify-content: space-between; align-items: center; border-top: 1px solid #ebeef5;">
                <div style="color: #909399; font-size: 14px;">
                    显示第 1-5 条记录，共 5 条
                </div>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <select class="form-select" style="min-width: 80px;">
                        <option>10条/页</option>
                        <option>20条/页</option>
                        <option>50条/页</option>
                    </select>
                    <button class="btn btn-default btn-small">上一页</button>
                    <span style="padding: 0 10px; color: #409EFF;">1</span>
                    <button class="btn btn-default btn-small">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 弹窗组件 -->
    <div id="modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">标题</h3>
                <span class="modal-close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body" id="modal-body">
                内容
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" id="modal-confirm">确定</button>
            </div>
        </div>
    </div>

    <style>
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 90vh;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #303133;
        }

        .modal-close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #909399;
        }

        .modal-close:hover {
            color: #f56c6c;
        }

        .modal-body {
            padding: 30px;
            overflow-y: auto;
            flex: 1;
            min-height: 0;
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid #ebeef5;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
    </style>

    <script>
        // 弹窗功能
        function showModal(title, content, onConfirm) {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-body').innerHTML = content;
            document.getElementById('modal').style.display = 'block';

            const confirmBtn = document.getElementById('modal-confirm');
            confirmBtn.onclick = function() {
                if (onConfirm) onConfirm();
                closeModal();
            };
        }

        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('modal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 模拟交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 按钮点击效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                    
                    // 模拟操作提示
                    if (this.textContent.includes('创建过滤器组')) {
                        showModal('创建过滤器组', `
                            <form>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold; width: 120px;">组名称 *</td>
                                        <td style="padding: 8px;">
                                            <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" placeholder="请输入过滤器组名称">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">消息标识 *</td>
                                        <td style="padding: 8px;">
                                            <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" placeholder="如: SYSTEM_ALERT">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">弹窗类型 *</td>
                                        <td style="padding: 8px;">
                                            <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                                <option value="">请选择</option>
                                                <option value="NOTIFICATION">通知</option>
                                                <option value="POPUP_WINDOW">弹窗</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">执行方式</td>
                                        <td style="padding: 8px;">
                                            <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                                <option value="CHAIN" selected>责任链</option>
                                                <option value="LOGIC">逻辑运算</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">描述</td>
                                        <td style="padding: 8px;">
                                            <textarea style="width: 100%; height: 60px; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" placeholder="请输入描述"></textarea>
                                        </td>
                                    </tr>
                                </table>
                            </form>
                        `, function() {
                            alert('过滤器组创建成功！');
                        });
                    } else if (this.textContent.includes('编辑')) {
                        showModal('编辑过滤器组', `
                            <form>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold; width: 120px;">组名称 *</td>
                                        <td style="padding: 8px;">
                                            <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="系统告警过滤组">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">消息标识 *</td>
                                        <td style="padding: 8px;">
                                            <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="SYSTEM_ALERT">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">弹窗类型 *</td>
                                        <td style="padding: 8px;">
                                            <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                                <option value="NOTIFICATION">通知</option>
                                                <option value="POPUP_WINDOW" selected>弹窗</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">状态</td>
                                        <td style="padding: 8px;">
                                            <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                                <option value="ENABLED" selected>启用</option>
                                                <option value="DISABLED">禁用</option>
                                            </select>
                                        </td>
                                    </tr>
                                </table>
                            </form>
                        `, function() {
                            alert('过滤器组更新成功！');
                        });
                    } else if (this.textContent.includes('规则配置')) {
                        // 跳转到规则关联配置页面
                        window.open('rule-relation-config.html', '_blank');
                    } else if (this.textContent.includes('测试')) {
                        showModal('测试过滤器组', `
                            <div>
                                <h4 style="margin-bottom: 15px;">测试参数</h4>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold; width: 120px;">消息标识</td>
                                        <td style="padding: 8px;">
                                            <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="SYSTEM_ALERT" readonly>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">用户ID</td>
                                        <td style="padding: 8px;">
                                            <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" placeholder="请输入测试用户ID">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">测试时间</td>
                                        <td style="padding: 8px;">
                                            <input type="datetime-local" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                        </td>
                                    </tr>
                                </table>
                                <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px;">
                                    <h5 style="margin-bottom: 10px;">预期测试结果：</h5>
                                    <div style="font-size: 14px; color: #67c23a;">✓ 允许通过 - 满足所有过滤条件</div>
                                </div>
                            </div>
                        `, function() {
                            alert('测试执行完成！结果：允许通过');
                        });
                    } else if (this.textContent.includes('搜索')) {
                        alert('模拟：执行搜索操作');
                    }
                });
            });
            
            // 表格行悬停效果
            const rows = document.querySelectorAll('.data-table tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f5f7fa';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });
        });
    </script>
</body>
</html>
