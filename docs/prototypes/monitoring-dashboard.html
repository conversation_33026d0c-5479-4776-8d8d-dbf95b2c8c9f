<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监控统计 - 原型图</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: white;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-header h1 {
            color: #303133;
            font-size: 24px;
            margin: 0;
        }
        
        .time-selector {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .time-selector select {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-value.success {
            color: #67c23a;
        }
        
        .stat-value.primary {
            color: #409EFF;
        }
        
        .stat-value.warning {
            color: #e6a23c;
        }
        
        .stat-value.danger {
            color: #f56c6c;
        }
        
        .stat-label {
            color: #909399;
            font-size: 14px;
        }
        
        .stat-trend {
            font-size: 12px;
            margin-top: 5px;
        }
        
        .trend-up {
            color: #67c23a;
        }
        
        .trend-down {
            color: #f56c6c;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .chart-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .chart-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            background: #fafafa;
        }
        
        .chart-header h3 {
            color: #303133;
            font-size: 16px;
            margin: 0;
        }
        
        .chart-content {
            height: 300px;
            padding: 20px;
        }
        
        .table-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-header h3 {
            color: #303133;
            font-size: 16px;
            margin: 0;
        }
        
        .table-filters {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .table-filters select, .table-filters input {
            padding: 6px 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #909399;
            font-size: 14px;
            border-bottom: 1px solid #ebeef5;
        }
        
        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;
        }
        
        .data-table tr:hover {
            background: #f5f7fa;
        }
        
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-success {
            background: #f0f9ff;
            color: #67c23a;
            border: 1px solid #e1f3d8;
        }
        
        .status-error {
            background: #fef0f0;
            color: #f56c6c;
            border: 1px solid #fde2e2;
        }
        
        .back-link {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .back-link:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }
        
        @media (max-width: 1200px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .table-filters {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-link">← 返回原型首页</a>
        
        <!-- 页面头部 -->
        <div class="page-header">
            <h1>监控统计</h1>
            <div class="time-selector">
                <label>时间范围：</label>
                <select>
                    <option>今天</option>
                    <option selected>最近7天</option>
                    <option>最近30天</option>
                    <option>自定义</option>
                </select>
                <button style="padding: 8px 16px; background: #409EFF; color: white; border: none; border-radius: 4px; cursor: pointer;">🔄 刷新</button>
            </div>
        </div>
        
        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-value primary">12,456</div>
                <div class="stat-label">总执行次数</div>
                <div class="stat-trend trend-up">↗ 较昨日 +8.5%</div>
            </div>
            <div class="stat-card">
                <div class="stat-value success">98.2%</div>
                <div class="stat-label">成功率</div>
                <div class="stat-trend trend-up">↗ 较昨日 +0.3%</div>
            </div>
            <div class="stat-card">
                <div class="stat-value warning">85ms</div>
                <div class="stat-label">平均执行时间</div>
                <div class="stat-trend trend-down">↘ 较昨日 -5ms</div>
            </div>
            <div class="stat-card">
                <div class="stat-value danger">5</div>
                <div class="stat-label">活跃插件数</div>
                <div class="stat-trend">→ 无变化</div>
            </div>
        </div>
        
        <!-- 图表区域 -->
        <div class="charts-section">
            <div class="chart-card">
                <div class="chart-header">
                    <h3>执行趋势</h3>
                </div>
                <div class="chart-content">
                    <div id="trendChart" style="width: 100%; height: 100%;"></div>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-header">
                    <h3>插件使用分布</h3>
                </div>
                <div class="chart-content">
                    <div id="pluginChart" style="width: 100%; height: 100%;"></div>
                </div>
            </div>
        </div>
        
        <!-- 详细统计表格 -->
        <div class="table-section">
            <div class="table-header">
                <h3>详细统计</h3>
                <div class="table-filters">
                    <select>
                        <option value="">全部插件</option>
                        <option value="time_filter">时间过滤插件</option>
                        <option value="user_filter">用户过滤插件</option>
                        <option value="repeat_filter">重复规则插件</option>
                    </select>
                    <select>
                        <option value="">全部状态</option>
                        <option value="success">成功</option>
                        <option value="error">错误</option>
                    </select>
                    <input type="text" placeholder="搜索规则名称">
                </div>
            </div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>插件名称</th>
                        <th>规则名称</th>
                        <th>执行次数</th>
                        <th>成功次数</th>
                        <th>失败次数</th>
                        <th>成功率</th>
                        <th>平均执行时间(ms)</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>基础开关过滤插件</td>
                        <td>系统开关规则</td>
                        <td>3,245</td>
                        <td>3,245</td>
                        <td>0</td>
                        <td>100.0%</td>
                        <td>12</td>
                        <td><span class="status-tag status-success">正常</span></td>
                    </tr>
                    <tr>
                        <td>增强时间段过滤插件</td>
                        <td>工作时间规则</td>
                        <td>2,856</td>
                        <td>2,801</td>
                        <td>55</td>
                        <td>98.1%</td>
                        <td>45</td>
                        <td><span class="status-tag status-success">正常</span></td>
                    </tr>
                    <tr>
                        <td>用户权限过滤插件</td>
                        <td>管理员用户规则</td>
                        <td>2,134</td>
                        <td>2,089</td>
                        <td>45</td>
                        <td>97.9%</td>
                        <td>78</td>
                        <td><span class="status-tag status-success">正常</span></td>
                    </tr>
                    <tr>
                        <td>重复规则过滤插件</td>
                        <td>仅工作日规则</td>
                        <td>1,967</td>
                        <td>1,923</td>
                        <td>44</td>
                        <td>97.8%</td>
                        <td>23</td>
                        <td><span class="status-tag status-success">正常</span></td>
                    </tr>
                    <tr>
                        <td>频率限制过滤插件</td>
                        <td>频率限制规则</td>
                        <td>1,456</td>
                        <td>1,398</td>
                        <td>58</td>
                        <td>96.0%</td>
                        <td>156</td>
                        <td><span class="status-tag status-success">正常</span></td>
                    </tr>

                </tbody>
            </table>
            
            <!-- 分页 -->
            <div style="padding: 20px 30px; display: flex; justify-content: space-between; align-items: center; border-top: 1px solid #ebeef5;">
                <div style="color: #909399; font-size: 14px;">
                    显示第 1-5 条记录，共 5 条
                </div>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <select style="padding: 4px 8px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 12px;">
                        <option>10条/页</option>
                        <option>20条/页</option>
                        <option>50条/页</option>
                    </select>
                    <button style="padding: 4px 8px; background: #f4f4f5; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 12px;">上一页</button>
                    <span style="padding: 0 10px; color: #409EFF;">1</span>
                    <button style="padding: 4px 8px; background: #f4f4f5; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 12px;">下一页</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 执行趋势图
            const trendChart = echarts.init(document.getElementById('trendChart'));
            const trendOption = {
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['总执行次数', '成功次数', '失败次数']
                },
                xAxis: {
                    type: 'category',
                    data: ['01-12', '01-13', '01-14', '01-15', '01-16', '01-17', '01-18']
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '总执行次数',
                        type: 'line',
                        data: [1200, 1350, 1180, 1420, 1680, 1890, 2100],
                        smooth: true,
                        itemStyle: { color: '#409EFF' }
                    },
                    {
                        name: '成功次数',
                        type: 'line',
                        data: [1180, 1320, 1150, 1390, 1650, 1850, 2060],
                        smooth: true,
                        itemStyle: { color: '#67c23a' }
                    },
                    {
                        name: '失败次数',
                        type: 'line',
                        data: [20, 30, 30, 30, 30, 40, 40],
                        smooth: true,
                        itemStyle: { color: '#f56c6c' }
                    }
                ]
            };
            trendChart.setOption(trendOption);
            
            // 插件使用分布图
            const pluginChart = echarts.init(document.getElementById('pluginChart'));
            const pluginOption = {
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [
                    {
                        name: '插件使用次数',
                        type: 'pie',
                        radius: '50%',
                        data: [
                            { value: 3245, name: '基础开关过滤插件' },
                            { value: 2856, name: '时间段过滤插件' },
                            { value: 2134, name: '用户权限过滤插件' },
                            { value: 1967, name: '重复规则过滤插件' },
                            { value: 1456, name: '频率限制过滤插件' }
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };
            pluginChart.setOption(pluginOption);
            
            // 响应式处理
            window.addEventListener('resize', function() {
                trendChart.resize();
                pluginChart.resize();
            });
        });
    </script>
</body>
</html>
