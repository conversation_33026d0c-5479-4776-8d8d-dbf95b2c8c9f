<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则管理 - 原型图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: white;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-header h1 {
            color: #303133;
            font-size: 24px;
            margin: 0;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #409EFF;
            color: white;
        }
        
        .btn-primary:hover {
            background: #337ecc;
        }
        
        .btn-default {
            background: #f4f4f5;
            color: #606266;
            border: 1px solid #dcdfe6;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }

        .btn-info {
            background: #909399;
            color: white;
        }

        .btn-info:hover {
            background: #73767a;
        }

        .btn-success {
            background: #67c23a;
            color: white;
        }

        .btn-success:hover {
            background: #529b2e;
        }

        .btn-warning {
            background: #e6a23c;
            color: white;
        }

        .btn-warning:hover {
            background: #b88230;
        }

        .btn-danger {
            background: #f56c6c;
            color: white;
        }

        .btn-danger:hover {
            background: #dd6161;
        }

        .search-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px 30px;
            margin-bottom: 20px;
        }

        .search-form {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .search-input, .search-select {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
        }

        .search-input {
            flex: 1;
            min-width: 200px;
        }

        .search-select {
            min-width: 150px;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .table-footer {
            padding: 15px 30px;
            border-top: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pagination-info {
            color: #909399;
            font-size: 14px;
        }

        .pagination {
            display: flex;
            gap: 5px;
        }

        .table-info {
            color: #909399;
            font-size: 14px;
        }
        
        .content-layout {
            display: block;
        }
        
        .table-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-header h3 {
            color: #303133;
            font-size: 16px;
            margin: 0;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #909399;
            font-size: 14px;
            border-bottom: 1px solid #ebeef5;
        }
        
        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;
        }
        
        .data-table tr:hover {
            background: #f5f7fa;
        }
        
        .data-table tr.selected {
            background: #ecf5ff;
        }
        
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-enabled {
            background: #f0f9ff;
            color: #67c23a;
            border: 1px solid #e1f3d8;
        }
        
        .status-disabled {
            background: #fdf6ec;
            color: #e6a23c;
            border: 1px solid #faecd8;
        }
        
        .form-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .form-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            background: #fafafa;
        }
        
        .form-header h3 {
            color: #303133;
            font-size: 16px;
            margin: 0;
        }
        
        .form-content {
            padding: 30px;
        }
        
        .form-item {
            margin-bottom: 20px;
        }
        
        .form-item label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #606266;
            font-weight: 500;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #409EFF;
        }
        
        .dynamic-config {
            border: 1px solid #ebeef5;
            border-radius: 4px;
            padding: 20px;
            background: #fafafa;
        }
        
        .dynamic-config h4 {
            color: #303133;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .config-field {
            margin-bottom: 15px;
        }
        
        .config-field:last-child {
            margin-bottom: 0;
        }
        
        .form-actions {
            padding: 20px 30px;
            border-top: 1px solid #ebeef5;
            background: #fafafa;
            display: flex;
            gap: 10px;
        }
        
        .back-link {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .back-link:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }
        
        @media (max-width: 1200px) {
            .content-layout {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-link">← 返回原型首页</a>
        
        <!-- 页面头部 -->
        <div class="page-header">
            <h1>规则管理</h1>
            <div>
                <button class="btn btn-primary">➕ 创建规则</button>
            </div>
        </div>
        
        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-form">
                <input type="text" placeholder="搜索规则名称..." class="search-input">
                <select class="search-select">
                    <option value="">所有插件</option>
                    <option value="basic_switch_filter">基础开关过滤插件</option>
                    <option value="enhanced_time_filter">增强时间段过滤插件</option>
                    <option value="repeat_rule_filter">重复规则过滤插件</option>
                    <option value="user_permission_filter">用户权限过滤插件</option>
                    <option value="frequency_limit_filter">频率限制过滤插件</option>
                    <option value="custom_oa_approval">OA审批过滤插件</option>
                    <option value="custom_business_rule">业务规则过滤插件</option>
                </select>
                <select class="search-select">
                    <option value="">所有状态</option>
                    <option value="enabled">启用</option>
                    <option value="disabled">禁用</option>
                </select>
                <button class="btn btn-primary">🔍 搜索</button>
            </div>
        </div>

        <!-- 规则列表表格 -->
        <div class="table-section">
            <div class="table-header">
                <h3>规则列表</h3>
                <div class="table-info">共 6 条记录</div>
            </div>
                
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>规则ID</th>
                            <th>规则名称</th>
                            <th>所属插件</th>
                            <th>优先级</th>
                            <th>超时(ms)</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>rule_work_hours</td>
                            <td>工作时间规则</td>
                            <td>增强时间段过滤插件</td>
                            <td>1</td>
                            <td><span style="color: #409EFF; font-size: 12px;">1500</span></td>
                            <td><span class="status-tag status-enabled">启用</span></td>
                            <td>2024-01-15 10:30:00</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-info btn-small">详情</button>
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-success btn-small">测试</button>
                                    <button class="btn btn-warning btn-small">禁用</button>
                                    <button class="btn btn-danger btn-small">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>rule_admin_users</td>
                            <td>管理员用户规则</td>
                            <td>用户权限过滤插件</td>
                            <td>2</td>
                            <td><span style="color: #409EFF; font-size: 12px;">1800</span></td>
                            <td><span class="status-tag status-enabled">启用</span></td>
                            <td>2024-01-15 10:35:00</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-info btn-small">详情</button>
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-success btn-small">测试</button>
                                    <button class="btn btn-warning btn-small">禁用</button>
                                    <button class="btn btn-danger btn-small">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>rule_weekday_only</td>
                            <td>仅工作日规则</td>
                            <td>重复规则过滤插件</td>
                            <td>3</td>
                            <td><span style="color: #409EFF; font-size: 12px;">1200</span></td>
                            <td><span class="status-tag status-enabled">启用</span></td>
                            <td>2024-01-15 10:40:00</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-info btn-small">详情</button>
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-success btn-small">测试</button>
                                    <button class="btn btn-warning btn-small">禁用</button>
                                    <button class="btn btn-danger btn-small">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>rule_frequency_limit</td>
                            <td>频率限制规则</td>
                            <td>频率限制过滤插件</td>
                            <td>4</td>
                            <td><span style="color: #409EFF; font-size: 12px;">2500</span></td>
                            <td><span class="status-tag status-enabled">启用</span></td>
                            <td>2024-01-16 09:15:00</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-info btn-small">详情</button>
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-success btn-small">测试</button>
                                    <button class="btn btn-warning btn-small">禁用</button>
                                    <button class="btn btn-danger btn-small">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>rule_oa_approval</td>
                            <td>OA审批规则</td>
                            <td>OA审批过滤插件</td>
                            <td>5</td>
                            <td><span style="color: #409EFF; font-size: 12px;">5000</span></td>
                            <td><span class="status-tag status-disabled">禁用</span></td>
                            <td>2024-01-16 14:20:00</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-info btn-small">详情</button>
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-success btn-small">测试</button>
                                    <button class="btn btn-success btn-small">启用</button>
                                    <button class="btn btn-danger btn-small">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>rule_business_status</td>
                            <td>业务状态规则</td>
                            <td>业务规则过滤插件</td>
                            <td>6</td>
                            <td><span style="color: #409EFF; font-size: 12px;">3000</span></td>
                            <td><span class="status-tag status-enabled">启用</span></td>
                            <td>2024-01-17 11:45:00</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-info btn-small">详情</button>
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-success btn-small">测试</button>
                                    <button class="btn btn-warning btn-small">禁用</button>
                                    <button class="btn btn-danger btn-small">删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- 分页信息 -->
                <div class="table-footer">
                    <div class="pagination-info">
                        显示第 1-6 条记录，共 6 条
                    </div>
                    <div class="pagination">
                        <button class="btn btn-default btn-small" disabled>上一页</button>
                        <button class="btn btn-primary btn-small">1</button>
                        <button class="btn btn-default btn-small" disabled>下一页</button>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- 弹窗组件 -->
    <div id="modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">标题</h3>
                <span class="modal-close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body" id="modal-body">
                内容
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" id="modal-confirm">确定</button>
            </div>
        </div>
    </div>

    <style>
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 90vh;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #303133;
        }

        .modal-close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #909399;
        }

        .modal-close:hover {
            color: #f56c6c;
        }

        .modal-body {
            padding: 30px;
            overflow-y: auto;
            flex: 1;
            min-height: 0;
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid #ebeef5;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
    </style>

    <script>
        // 弹窗功能
        function showModal(title, content, onConfirm) {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-body').innerHTML = content;
            document.getElementById('modal').style.display = 'block';

            const confirmBtn = document.getElementById('modal-confirm');
            confirmBtn.onclick = function() {
                if (onConfirm) onConfirm();
                closeModal();
            };
        }

        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('modal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 插件配置模板
        function getPluginConfigTemplate(pluginId, isEdit = false) {
            const configs = {
                'basic_switch_filter': `
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px; font-weight: bold; width: 120px;">启用状态 *</td>
                            <td style="padding: 8px;">
                                <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="true" ${isEdit ? 'selected' : ''}>启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </td>
                        </tr>
                    </table>
                `,
                'enhanced_time_filter': `
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px; font-weight: bold; width: 120px;">开始时间 *</td>
                            <td style="padding: 8px;">
                                <input type="time" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="${isEdit ? '09:00' : ''}">
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; font-weight: bold;">结束时间 *</td>
                            <td style="padding: 8px;">
                                <input type="time" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="${isEdit ? '18:00' : ''}">
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; font-weight: bold;">允许跨天</td>
                            <td style="padding: 8px;">
                                <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="false" selected>否</option>
                                    <option value="true">是</option>
                                </select>
                            </td>
                        </tr>
                    </table>
                `,
                'repeat_rule_filter': `
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px; font-weight: bold; width: 120px;">重复类型 *</td>
                            <td style="padding: 8px;">
                                <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="DAILY">每天</option>
                                    <option value="WEEKDAY" ${isEdit ? 'selected' : ''}>工作日</option>
                                    <option value="WEEKEND">周末</option>
                                    <option value="HOLIDAY">节假日</option>
                                    <option value="ONCE">执行一次</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; font-weight: bold;">执行时间</td>
                            <td style="padding: 8px;">
                                <input type="datetime-local" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                <div style="font-size: 12px; color: #909399; margin-top: 5px;">仅"执行一次"类型需要设置</div>
                            </td>
                        </tr>
                    </table>
                `,
                'user_permission_filter': `
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px; font-weight: bold; width: 120px;">允许的角色 *</td>
                            <td style="padding: 8px;">
                                <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="${isEdit ? 'ADMIN,MANAGER' : ''}" placeholder="多个角色用逗号分隔">
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; font-weight: bold;">禁止的用户</td>
                            <td style="padding: 8px;">
                                <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" placeholder="多个用户ID用逗号分隔">
                            </td>
                        </tr>
                    </table>
                `,
                'frequency_limit_filter': `
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px; font-weight: bold; width: 120px;">时间窗口(分钟) *</td>
                            <td style="padding: 8px;">
                                <input type="number" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="${isEdit ? '60' : ''}" min="1" max="1440">
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; font-weight: bold;">最大次数 *</td>
                            <td style="padding: 8px;">
                                <input type="number" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="${isEdit ? '5' : ''}" min="1" max="100">
                            </td>
                        </tr>
                    </table>
                `,
                'custom_oa_approval': `
                    <div style="text-align: center; padding: 20px; color: #909399; background: #f8f9fa; border-radius: 4px;">
                        <p><strong>自定义插件无需参数配置</strong></p>
                        <div style="margin-top: 15px; padding: 15px; background: #fff; border-radius: 4px; text-align: left; border: 1px solid #eee;">
                            <div style="font-size: 12px; color: #606266;">
                                <div><strong>类名:</strong> com.company.business.filter.OAApprovalFilter</div>
                                <div style="margin-top: 5px;"><strong>方法:</strong> checkApprovalStatus()</div>
                                <div style="margin-top: 5px;"><strong>说明:</strong> 插件内部封装OA审批逻辑，直接返回过滤结果</div>
                            </div>
                        </div>
                    </div>
                `,
                'custom_business_rule': `
                    <div style="text-align: center; padding: 20px; color: #909399; background: #f8f9fa; border-radius: 4px;">
                        <p><strong>自定义插件无需参数配置</strong></p>
                        <div style="margin-top: 15px; padding: 15px; background: #fff; border-radius: 4px; text-align: left; border: 1px solid #eee;">
                            <div style="font-size: 12px; color: #606266;">
                                <div><strong>类名:</strong> com.company.business.filter.BusinessRuleFilter</div>
                                <div style="margin-top: 5px;"><strong>方法:</strong> validateBusinessRule()</div>
                                <div style="margin-top: 5px;"><strong>说明:</strong> 插件内部封装复杂业务规则验证逻辑</div>
                            </div>
                        </div>
                    </div>
                `
            };

            return configs[pluginId] || `
                <div style="text-align: center; padding: 20px; color: #909399; background: #f8f9fa; border-radius: 4px;">
                    请先选择插件
                </div>
            `;
        }

        // 更新创建规则的插件配置
        function updateCreatePluginConfig(pluginId) {
            const configDiv = document.getElementById('create-plugin-config');
            if (configDiv) {
                const content = pluginId ? getPluginConfigTemplate(pluginId, false) : `
                    <div style="text-align: center; padding: 20px; color: #909399; background: #f8f9fa; border-radius: 4px;">
                        请先选择插件
                    </div>
                `;
                configDiv.innerHTML = '<h4 style="margin-bottom: 10px;">插件配置参数</h4>' + content;
            }
        }

        // 更新编辑规则的插件配置
        function updateEditPluginConfig(pluginId) {
            const configDiv = document.getElementById('edit-config-content');
            if (configDiv) {
                const content = pluginId ? getPluginConfigTemplate(pluginId, true) : `
                    <div style="text-align: center; padding: 20px; color: #909399; background: #f8f9fa; border-radius: 4px;">
                        请先选择插件
                    </div>
                `;
                configDiv.innerHTML = content;
            }
        }




        // 规则选择功能
        function selectRule(row, ruleId) {
            // 移除所有选中状态
            document.querySelectorAll('.data-table tr').forEach(tr => {
                tr.classList.remove('selected');
            });
            
            // 添加选中状态
            row.classList.add('selected');
            
            // 模拟加载规则配置
            loadRuleConfig(ruleId);
        }
        
        function loadRuleConfig(ruleId) {
            // 模拟不同规则的配置
            const configs = {
                'rule_work_hours': {
                    name: '工作时间规则',
                    plugin: 'enhanced_time_filter',
                    description: '只在工作时间（09:00-18:00）允许弹窗通知',
                    priority: 1,
                    timeout: 1000,
                    config: {
                        startTime: '09:00',
                        endTime: '18:00',
                        allowCrossDay: false
                    }
                },
                'rule_admin_users': {
                    name: '管理员用户规则',
                    plugin: 'user_permission_filter',
                    description: '只允许管理员和经理角色接收通知',
                    priority: 2,
                    timeout: 500,
                    config: {
                        allowedRoles: ['ADMIN', 'MANAGER'],
                        blockedUsers: []
                    }
                }
            };
            
            const config = configs[ruleId] || configs['rule_work_hours'];
            
            // 更新表单
            document.querySelector('input[placeholder="请输入规则名称"]').value = config.name;
            document.querySelector('.form-select').value = config.plugin;
            document.querySelector('.form-textarea').value = config.description;
            document.querySelector('input[type="number"][min="0"]').value = config.priority;
            document.querySelector('input[type="number"][min="100"]').value = config.timeout;
            
            // 更新动态配置（这里简化处理）
            if (config.config.startTime) {
                document.querySelector('input[type="time"]:first-of-type').value = config.config.startTime;
            }
            if (config.config.endTime) {
                document.querySelector('input[type="time"]:last-of-type').value = config.config.endTime;
            }
        }
        
        // 按钮交互
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                    
                    if (this.textContent.includes('创建规则')) {
                        showModal('创建新规则', `
                            <div>
                                <h4 style="margin-bottom: 15px;">创建过滤规则</h4>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold; width: 120px;">规则名称 *</td>
                                        <td style="padding: 8px;">
                                            <input type="text" id="create-rule-name" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" placeholder="请输入规则名称">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">选择插件 *</td>
                                        <td style="padding: 8px;">
                                            <select id="create-plugin-select" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" onchange="updateCreatePluginConfig(this.value)">
                                                <option value="">请选择插件</option>
                                                <optgroup label="内置插件">
                                                    <option value="basic_switch_filter">基础开关过滤插件</option>
                                                    <option value="enhanced_time_filter">增强时间段过滤插件</option>
                                                    <option value="repeat_rule_filter">重复规则过滤插件</option>
                                                    <option value="user_permission_filter">用户权限过滤插件</option>
                                                    <option value="frequency_limit_filter">频率限制过滤插件</option>
                                                </optgroup>
                                                <optgroup label="自定义插件">
                                                    <option value="custom_oa_approval">OA审批过滤插件</option>
                                                    <option value="custom_business_rule">业务规则过滤插件</option>
                                                </optgroup>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">优先级</td>
                                        <td style="padding: 8px;">
                                            <input type="number" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="1" min="1" max="100">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">超时时间(ms)</td>
                                        <td style="padding: 8px;">
                                            <input type="number" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="1000" min="100" max="30000">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">规则描述</td>
                                        <td style="padding: 8px;">
                                            <textarea style="width: 100%; height: 60px; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" placeholder="请输入规则描述"></textarea>
                                        </td>
                                    </tr>
                                </table>

                                <!-- 插件参数配置区域 -->
                                <div id="create-plugin-config" style="margin-top: 20px;">
                                    <h4 style="margin-bottom: 10px;">插件配置参数</h4>
                                    <div style="text-align: center; padding: 20px; color: #909399; background: #f8f9fa; border-radius: 4px;">
                                        请先选择插件
                                    </div>
                                </div>
                            </div>
                        `, function() {
                            alert('规则创建成功！');
                        });
                    } else if (this.textContent.includes('详情')) {
                        // 根据点击的行获取规则信息
                        const row = this.closest('tr');
                        const ruleId = row.cells[0].textContent;
                        const ruleName = row.cells[1].textContent;
                        const pluginName = row.cells[2].textContent;
                        const priority = row.cells[3].textContent;
                        const timeout = row.cells[4].textContent.trim();
                        const status = row.cells[5].textContent.trim();
                        const createTime = row.cells[6].textContent;

                        showModal('规则详情', `
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold; width: 120px;">规则ID:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${ruleId}</td></tr>
                                <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">规则名称:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${ruleName}</td></tr>
                                <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">所属插件:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${pluginName}</td></tr>
                                <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">优先级:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${priority}</td></tr>
                                <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">超时时间:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${timeout}</td></tr>
                                <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">状态:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${status}</td></tr>
                                <tr><td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">创建时间:</td><td style="padding: 8px; border-bottom: 1px solid #eee;">${createTime}</td></tr>
                                <tr><td style="padding: 8px; font-weight: bold;">规则配置:</td><td style="padding: 8px;">
                                    <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px;">
                                        {<br>
                                        &nbsp;&nbsp;"startTime": "09:00",<br>
                                        &nbsp;&nbsp;"endTime": "18:00",<br>
                                        &nbsp;&nbsp;"allowCrossDay": false<br>
                                        }
                                    </div>
                                </td></tr>
                            </table>
                        `);
                    } else if (this.textContent.includes('编辑')) {
                        const row = this.closest('tr');
                        const ruleId = row.cells[0].textContent;
                        const ruleName = row.cells[1].textContent;
                        const pluginName = row.cells[2].textContent;
                        const priority = row.cells[3].textContent;
                        const timeout = row.cells[4].textContent.trim();

                        // 根据插件名称获取插件ID
                        const pluginIdMap = {
                            '基础开关过滤插件': 'basic_switch_filter',
                            '增强时间段过滤插件': 'enhanced_time_filter',
                            '重复规则过滤插件': 'repeat_rule_filter',
                            '用户权限过滤插件': 'user_permission_filter',
                            '频率限制过滤插件': 'frequency_limit_filter',
                            'OA审批过滤插件': 'custom_oa_approval',
                            '业务规则过滤插件': 'custom_business_rule'
                        };
                        const pluginId = pluginIdMap[pluginName] || '';

                        showModal('编辑规则', `
                            <div>
                                <h4 style="margin-bottom: 15px;">编辑过滤规则</h4>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold; width: 120px;">规则ID</td>
                                        <td style="padding: 8px;">
                                            <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px; background: #f5f5f5;" value="${ruleId}" readonly>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">规则名称 *</td>
                                        <td style="padding: 8px;">
                                            <input type="text" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="${ruleName}">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">所属插件</td>
                                        <td style="padding: 8px;">
                                            <select id="edit-plugin-select" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" onchange="updateEditPluginConfig(this.value)">
                                                <optgroup label="内置插件">
                                                    <option value="basic_switch_filter" ${pluginId === 'basic_switch_filter' ? 'selected' : ''}>基础开关过滤插件</option>
                                                    <option value="enhanced_time_filter" ${pluginId === 'enhanced_time_filter' ? 'selected' : ''}>增强时间段过滤插件</option>
                                                    <option value="repeat_rule_filter" ${pluginId === 'repeat_rule_filter' ? 'selected' : ''}>重复规则过滤插件</option>
                                                    <option value="user_permission_filter" ${pluginId === 'user_permission_filter' ? 'selected' : ''}>用户权限过滤插件</option>
                                                    <option value="frequency_limit_filter" ${pluginId === 'frequency_limit_filter' ? 'selected' : ''}>频率限制过滤插件</option>
                                                </optgroup>
                                                <optgroup label="自定义插件">
                                                    <option value="custom_oa_approval" ${pluginId === 'custom_oa_approval' ? 'selected' : ''}>OA审批过滤插件</option>
                                                    <option value="custom_business_rule" ${pluginId === 'custom_business_rule' ? 'selected' : ''}>业务规则过滤插件</option>
                                                </optgroup>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">优先级</td>
                                        <td style="padding: 8px;">
                                            <input type="number" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="${priority}" min="1" max="100">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">超时时间(ms)</td>
                                        <td style="padding: 8px;">
                                            <input type="number" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="${timeout.replace(/[^0-9]/g, '')}" min="100" max="30000">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">规则描述</td>
                                        <td style="padding: 8px;">
                                            <textarea style="width: 100%; height: 60px; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">只在工作时间（09:00-18:00）允许弹窗通知</textarea>
                                        </td>
                                    </tr>
                                </table>

                                <!-- 插件参数配置区域 -->
                                <div id="edit-plugin-config" style="margin-top: 20px;">
                                    <h4 style="margin-bottom: 10px;">插件配置参数</h4>
                                    <div id="edit-config-content">
                                        <!-- 这里会动态加载插件配置 -->
                                    </div>
                                </div>
                            </div>
                        `, function() {
                            alert('规则更新成功！');
                        });

                        // 弹窗显示后，立即加载当前插件的配置
                        setTimeout(() => {
                            updateEditPluginConfig(pluginId);
                        }, 100);
                    } else if (this.textContent.includes('保存规则')) {
                        showModal('保存规则', `
                            <div style="text-align: center; padding: 20px;">
                                <div style="margin-bottom: 20px;">
                                    <div style="font-size: 48px; color: #67c23a; margin-bottom: 15px;">✓</div>
                                    <h4 style="color: #303133; margin-bottom: 10px;">规则保存成功</h4>
                                    <p style="color: #606266; font-size: 14px;">过滤规则配置已成功保存到数据库</p>
                                </div>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; text-align: left;">
                                    <div style="font-size: 12px; color: #606266;">
                                        <div><strong>规则ID:</strong> rule_time_filter_001</div>
                                        <div><strong>规则名称:</strong> 时间段过滤规则</div>
                                        <div><strong>插件类型:</strong> 增强时间段过滤插件</div>
                                        <div><strong>保存时间:</strong> ${new Date().toLocaleString()}</div>
                                    </div>
                                </div>
                            </div>
                        `);
                    } else if (this.textContent.includes('测试')) {
                        const row = this.closest('tr');
                        const ruleName = row.cells[1].textContent;
                        const pluginName = row.cells[2].textContent;

                        showModal('测试规则', `
                            <div>
                                <h4 style="margin-bottom: 15px;">测试规则: ${ruleName}</h4>
                                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold; width: 120px;">插件类型:</td>
                                        <td style="padding: 8px;">${pluginName}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">测试场景:</td>
                                        <td style="padding: 8px;">
                                            <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                                <option value="normal">正常工作时间</option>
                                                <option value="overtime">加班时间</option>
                                                <option value="weekend">周末时间</option>
                                                <option value="holiday">节假日时间</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">测试时间:</td>
                                        <td style="padding: 8px;">
                                            <input type="datetime-local" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">用户角色:</td>
                                        <td style="padding: 8px;">
                                            <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                                <option value="admin">管理员</option>
                                                <option value="user" selected>普通用户</option>
                                                <option value="guest">访客</option>
                                            </select>
                                        </td>
                                    </tr>
                                </table>
                                <div style="background: #e1f3d8; padding: 15px; border-radius: 4px;">
                                    <h5 style="color: #529b2e; margin-bottom: 10px;">测试结果预览:</h5>
                                    <div style="font-size: 14px; color: #529b2e;">
                                        <div>✓ 规则执行: 成功</div>
                                        <div>✓ 时间检查: 通过 (工作时间内)</div>
                                        <div>✓ 执行时间: 45ms</div>
                                        <div style="margin-top: 10px; font-weight: bold;">最终结果: 允许通过</div>
                                    </div>
                                </div>
                            </div>
                        `, function() {
                            alert('规则测试完成！');
                        });
                    } else if (this.textContent.includes('测试配置')) {
                        showModal('测试规则配置', `
                            <div>
                                <h4 style="margin-bottom: 15px;">规则配置测试</h4>
                                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold; width: 120px;">测试场景:</td>
                                        <td style="padding: 8px;">
                                            <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                                <option value="normal">正常时间段</option>
                                                <option value="cross_day">跨天时间段</option>
                                                <option value="weekend">周末时间</option>
                                                <option value="holiday">节假日时间</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">测试时间:</td>
                                        <td style="padding: 8px;">
                                            <input type="datetime-local" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">用户角色:</td>
                                        <td style="padding: 8px;">
                                            <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                                <option value="admin">管理员</option>
                                                <option value="user" selected>普通用户</option>
                                                <option value="guest">访客</option>
                                            </select>
                                        </td>
                                    </tr>
                                </table>
                                <div style="background: #e1f3d8; padding: 15px; border-radius: 4px;">
                                    <h5 style="color: #529b2e; margin-bottom: 10px;">测试结果预览:</h5>
                                    <div style="font-size: 14px; color: #529b2e;">
                                        <div>✓ 时间段检查: 通过 (09:00-18:00)</div>
                                        <div>✓ 跨天支持: 启用</div>
                                        <div>✓ 执行时间: < 50ms</div>
                                        <div style="margin-top: 10px; font-weight: bold;">最终结果: 允许通过</div>
                                    </div>
                                </div>
                            </div>
                        `, function() {
                            alert('规则测试完成！');
                        });
                    } else if (this.textContent.includes('创建规则')) {
                        showModal('创建新规则', `
                            <div style="text-align: center; padding: 20px;">
                                <div style="margin-bottom: 20px;">
                                    <div style="font-size: 48px; color: #409EFF; margin-bottom: 15px;">📝</div>
                                    <h4 style="color: #303133; margin-bottom: 10px;">创建新的过滤规则</h4>
                                    <p style="color: #606266; font-size: 14px;">表单将被清空，您可以开始配置新的过滤规则</p>
                                </div>
                                <div style="background: #ecf5ff; padding: 15px; border-radius: 4px; text-align: left;">
                                    <div style="font-size: 12px; color: #409EFF;">
                                        <div><strong>操作说明:</strong></div>
                                        <div>1. 选择要使用的插件类型</div>
                                        <div>2. 配置规则名称和描述</div>
                                        <div>3. 设置插件参数</div>
                                        <div>4. 保存规则配置</div>
                                    </div>
                                </div>
                            </div>
                        `, function() {
                            // 清空表单
                            document.getElementById('ruleName').value = '';
                            document.getElementById('ruleDescription').value = '';
                            document.getElementById('pluginSelect').selectedIndex = 0;
                            alert('表单已清空，可以开始创建新规则！');
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
