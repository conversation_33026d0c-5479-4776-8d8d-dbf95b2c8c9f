<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>勿扰规则配置 - 原型图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: white;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-header h1 {
            color: #303133;
            font-size: 24px;
            margin: 0;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #409EFF;
            color: white;
        }
        
        .btn-primary:hover {
            background: #337ecc;
        }
        
        .btn-default {
            background: #f4f4f5;
            color: #606266;
            border: 1px solid #dcdfe6;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .btn-success {
            background: #67c23a;
            color: white;
        }
        
        .btn-danger {
            background: #f56c6c;
            color: white;
        }
        
        .config-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .section-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            background: #fafafa;
        }
        
        .section-header h3 {
            color: #303133;
            font-size: 16px;
            margin: 0;
        }
        
        .section-content {
            padding: 30px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-item label {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
        }
        
        .form-input, .form-select {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #409EFF;
        }
        
        .radio-group {
            display: flex;
            gap: 20px;
        }
        
        .radio-item {
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }
        
        .radio-item input[type="radio"] {
            margin-top: 4px;
        }
        
        .radio-content {
            display: flex;
            flex-direction: column;
        }
        
        .radio-title {
            font-weight: 500;
            color: #303133;
        }
        
        .radio-desc {
            font-size: 12px;
            color: #909399;
            margin-top: 2px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .data-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #909399;
            font-size: 14px;
            border-bottom: 1px solid #ebeef5;
        }
        
        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;
        }
        
        .data-table tr:hover {
            background: #f5f7fa;
        }
        
        .table-input {
            width: 100%;
            padding: 4px 8px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .table-select {
            width: 100%;
            padding: 4px 8px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .table-actions {
            padding: 20px 30px;
            border-top: 1px solid #ebeef5;
            background: #fafafa;
            display: flex;
            gap: 10px;
        }
        
        .preview-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .preview-section h4 {
            color: #303133;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .logic-description {
            color: #606266;
            line-height: 1.6;
        }
        
        .logic-steps {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        
        .logic-steps li {
            padding: 8px 0;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .logic-steps li:last-child {
            border-bottom: none;
        }
        
        .step-number {
            background: #409EFF;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
        }
        
        .operator-tag {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .operator-and {
            background: #e1f3d8;
            color: #529b2e;
        }
        
        .operator-or {
            background: #fdf6ec;
            color: #b88230;
        }
        
        .operator-not {
            background: #fef0f0;
            color: #c45656;
        }
        
        .back-link {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .back-link:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .radio-group {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-link">← 返回原型首页</a>
        
        <!-- 页面头部 -->
        <div class="page-header">
            <h1>勿扰规则配置 - 系统告警勿扰模式</h1>
            <div>
                <button class="btn btn-default">🔍 预览执行逻辑</button>
                <button class="btn btn-primary">💾 保存配置</button>
            </div>
        </div>
        
        <!-- 基础配置 -->
        <div class="config-section">
            <div class="section-header">
                <h3>基础配置</h3>
            </div>
            <div class="section-content">
                <div class="form-row">
                    <div class="form-item">
                        <label>消息标识</label>
                        <input type="text" class="form-input" value="SYSTEM_ALERT" placeholder="请输入消息标识">
                    </div>
                    <div class="form-item">
                        <label>弹窗类型</label>
                        <select class="form-select">
                            <option value="NOTIFICATION">通知</option>
                            <option value="POPUP_WINDOW" selected>弹窗</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-item">
                    <label>执行方式</label>
                    <div class="radio-group">
                        <div class="radio-item">
                            <input type="radio" name="executionType" value="CHAIN" checked onchange="toggleExecutionType('CHAIN')">
                            <div class="radio-content">
                                <div class="radio-title">责任链方式</div>
                                <div class="radio-desc">按顺序执行规则，任一规则拒绝则立即停止，适合简单过滤场景</div>
                            </div>
                        </div>
                        <div class="radio-item">
                            <input type="radio" name="executionType" value="LOGIC" onchange="toggleExecutionType('LOGIC')">
                            <div class="radio-content">
                                <div class="radio-title">逻辑运算方式</div>
                                <div class="radio-desc">执行所有规则，根据逻辑操作符计算最终结果，适合复杂逻辑场景</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-item">
                        <label>默认动作</label>
                        <select class="form-select">
                            <option value="ALLOW">允许（无规则时默认允许）</option>
                            <option value="DENY" selected>拒绝（无规则时默认拒绝）</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <label>总超时时间(ms)</label>
                        <input type="number" class="form-input" value="5000" min="1000" max="30000">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 规则关联配置 -->
        <div class="config-section">
            <div class="section-header">
                <h3>规则关联配置</h3>
            </div>
            <div class="section-content">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>规则名称</th>
                            <th id="orderColumn">执行顺序</th>
                            <th id="operatorColumn" style="display: none;">逻辑操作符</th>
                            <th>必需执行</th>
                            <th>超时时间(ms)</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <select class="table-select">
                                    <option value="">请选择规则</option>
                                    <option value="rule_basic_switch" selected>基础开关规则</option>
                                    <option value="rule_time_filter">时间段过滤规则</option>
                                    <option value="rule_repeat_filter">重复规则过滤规则</option>
                                    <option value="rule_user_permission">用户权限规则</option>
                                    <option value="rule_frequency_limit">频率限制规则</option>
                                </select>
                            </td>
                            <td id="order1">
                                <input type="number" class="table-input" value="1" min="1">
                            </td>
                            <td id="operator1" style="display: none;">
                                <select class="table-select">
                                    <option value="AND" selected>AND（与）</option>
                                    <option value="OR">OR（或）</option>
                                    <option value="NOT">NOT（非）</option>
                                </select>
                            </td>
                            <td>
                                <select class="table-select">
                                    <option value="false" selected>否</option>
                                    <option value="true">是</option>
                                </select>
                            </td>
                            <td>
                                <input type="number" class="table-input" value="1000" min="100" max="10000">
                            </td>
                            <td>
                                <button class="btn btn-default btn-small">↑</button>
                                <button class="btn btn-default btn-small">↓</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <select class="table-select">
                                    <option value="">请选择规则</option>
                                    <option value="rule_basic_switch">基础开关规则</option>
                                    <option value="rule_time_filter" selected>时间段过滤规则</option>
                                    <option value="rule_repeat_filter">重复规则过滤规则</option>
                                    <option value="rule_user_permission">用户权限规则</option>
                                    <option value="rule_frequency_limit">频率限制规则</option>
                                </select>
                            </td>
                            <td id="order2">
                                <input type="number" class="table-input" value="2" min="1">
                            </td>
                            <td id="operator2" style="display: none;">
                                <select class="table-select">
                                    <option value="AND" selected>AND（与）</option>
                                    <option value="OR">OR（或）</option>
                                    <option value="NOT">NOT（非）</option>
                                </select>
                            </td>
                            <td>
                                <select class="table-select">
                                    <option value="false" selected>否</option>
                                    <option value="true">是</option>
                                </select>
                            </td>
                            <td>
                                <input type="number" class="table-input" value="1000" min="100" max="10000">
                            </td>
                            <td>
                                <button class="btn btn-default btn-small">↑</button>
                                <button class="btn btn-default btn-small">↓</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <select class="table-select">
                                    <option value="">请选择规则</option>
                                    <option value="rule_basic_switch">基础开关规则</option>
                                    <option value="rule_time_filter">时间段过滤规则</option>
                                    <option value="rule_repeat_filter" selected>重复规则过滤规则</option>
                                    <option value="rule_user_permission">用户权限规则</option>
                                    <option value="rule_frequency_limit">频率限制规则</option>
                                </select>
                            </td>
                            <td id="order3">
                                <input type="number" class="table-input" value="3" min="1">
                            </td>
                            <td id="operator3" style="display: none;">
                                <select class="table-select">
                                    <option value="AND" selected>AND（与）</option>
                                    <option value="OR">OR（或）</option>
                                    <option value="NOT">NOT（非）</option>
                                </select>
                            </td>
                            <td>
                                <select class="table-select">
                                    <option value="false" selected>否</option>
                                    <option value="true">是</option>
                                </select>
                            </td>
                            <td>
                                <input type="number" class="table-input" value="500" min="100" max="10000">
                            </td>
                            <td>
                                <button class="btn btn-default btn-small">↑</button>
                                <button class="btn btn-default btn-small">↓</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="table-actions">
                <button class="btn btn-success">➕ 添加规则</button>
                <button class="btn btn-default">📥 批量导入</button>
                <button class="btn btn-default" onclick="togglePreview()">👁️ 预览执行逻辑</button>
            </div>
        </div>
        
        <!-- 执行逻辑预览 -->
        <div class="preview-section" id="previewSection" style="display: none;">
            <h4>执行逻辑预览</h4>
            <div class="logic-description">
                <p><strong>执行方式：</strong><span id="executionModeText">责任链方式</span></p>
                <p><strong>执行顺序：</strong></p>
                <ul class="logic-steps" id="logicSteps">
                    <li>
                        <div class="step-number">1</div>
                        <span>基础开关规则</span>
                        <span class="operator-tag operator-and">AND</span>
                    </li>
                    <li>
                        <div class="step-number">2</div>
                        <span>时间段过滤规则</span>
                        <span class="operator-tag operator-and">AND</span>
                    </li>
                    <li>
                        <div class="step-number">3</div>
                        <span>重复规则过滤规则</span>
                        <span class="operator-tag operator-and">AND</span>
                    </li>
                </ul>
                <p><strong>逻辑说明：</strong><span id="logicExplanation">责任链方式：按顺序执行规则，任一规则拒绝则立即停止，不再执行后续规则。适合简单过滤场景。</span></p>
            </div>
        </div>
    </div>

    <!-- 弹窗组件 -->
    <div id="modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">标题</h3>
                <span class="modal-close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body" id="modal-body">
                内容
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" id="modal-confirm">确定</button>
            </div>
        </div>
    </div>

    <style>
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 90vh;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #303133;
        }

        .modal-close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #909399;
        }

        .modal-close:hover {
            color: #f56c6c;
        }

        .modal-body {
            padding: 30px;
            overflow-y: auto;
            flex: 1;
            min-height: 0;
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid #ebeef5;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
    </style>

    <script>
        // 弹窗功能
        function showModal(title, content, onConfirm) {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-body').innerHTML = content;
            document.getElementById('modal').style.display = 'block';

            const confirmBtn = document.getElementById('modal-confirm');
            confirmBtn.onclick = function() {
                if (onConfirm) onConfirm();
                closeModal();
            };
        }

        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('modal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 切换执行方式
        function toggleExecutionType(type) {
            const orderColumn = document.getElementById('orderColumn');
            const operatorColumn = document.getElementById('operatorColumn');
            const executionModeText = document.getElementById('executionModeText');
            const logicExplanation = document.getElementById('logicExplanation');
            
            if (type === 'CHAIN') {
                orderColumn.style.display = '';
                operatorColumn.style.display = 'none';
                // 隐藏所有操作符列
                document.querySelectorAll('[id^="operator"]').forEach(el => {
                    if (el.id !== 'operatorColumn') el.style.display = 'none';
                });
                // 显示所有顺序列
                document.querySelectorAll('[id^="order"]').forEach(el => {
                    if (el.id !== 'orderColumn') el.style.display = '';
                });
                
                executionModeText.textContent = '责任链方式';
                logicExplanation.textContent = '责任链方式：按顺序执行规则，任一规则拒绝则立即停止，不再执行后续规则。适合简单过滤场景。';
            } else {
                orderColumn.style.display = 'none';
                operatorColumn.style.display = '';
                // 显示所有操作符列
                document.querySelectorAll('[id^="operator"]').forEach(el => {
                    if (el.id !== 'operatorColumn') el.style.display = '';
                });
                // 隐藏所有顺序列
                document.querySelectorAll('[id^="order"]').forEach(el => {
                    if (el.id !== 'orderColumn') el.style.display = 'none';
                });
                
                executionModeText.textContent = '逻辑运算方式';
                logicExplanation.textContent = '逻辑运算方式：执行所有规则，根据逻辑操作符（AND/OR/NOT）综合判断最终结果。适合复杂逻辑场景。';
            }
        }
        
        // 切换预览显示
        function togglePreview() {
            const previewSection = document.getElementById('previewSection');
            if (previewSection.style.display === 'none' || previewSection.style.display === '') {
                previewSection.style.display = 'block';
            } else {
                previewSection.style.display = 'none';
            }
        }
        
        // 模拟交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                    
                    if (this.textContent.includes('保存配置')) {
                        showModal('保存配置', `
                            <div style="text-align: center; padding: 20px;">
                                <div style="margin-bottom: 20px;">
                                    <div style="font-size: 48px; color: #67c23a; margin-bottom: 15px;">✓</div>
                                    <h4 style="color: #303133; margin-bottom: 10px;">配置保存成功</h4>
                                    <p style="color: #606266; font-size: 14px;">规则关联配置已成功保存到数据库</p>
                                </div>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; text-align: left;">
                                    <div style="font-size: 12px; color: #606266;">
                                        <div><strong>过滤器组:</strong> 系统告警过滤组</div>
                                        <div><strong>执行方式:</strong> 责任链</div>
                                        <div><strong>规则数量:</strong> 3个</div>
                                        <div><strong>保存时间:</strong> ${new Date().toLocaleString()}</div>
                                    </div>
                                </div>
                            </div>
                        `);
                    } else if (this.textContent.includes('添加规则')) {
                        showModal('添加规则关联', `
                            <div>
                                <h4 style="margin-bottom: 15px;">添加新的规则关联</h4>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold; width: 120px;">选择规则 *</td>
                                        <td style="padding: 8px;">
                                            <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                                <option value="">请选择规则</option>
                                                <option value="rule_basic_switch">基础开关规则</option>
                                                <option value="rule_time_filter">时间段过滤规则</option>
                                                <option value="rule_repeat_filter">重复规则过滤规则</option>
                                                <option value="rule_user_permission">用户权限规则</option>
                                                <option value="rule_frequency_limit">频率限制规则</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">执行顺序</td>
                                        <td style="padding: 8px;">
                                            <input type="number" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;" value="4" min="1">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">逻辑操作符</td>
                                        <td style="padding: 8px;">
                                            <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                                <option value="AND" selected>AND (与)</option>
                                                <option value="OR">OR (或)</option>
                                                <option value="NOT">NOT (非)</option>
                                            </select>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        `, function() {
                            alert('规则关联添加成功！');
                        });
                    } else if (this.textContent.includes('批量导入')) {
                        showModal('批量导入规则', `
                            <div>
                                <h4 style="margin-bottom: 15px;">批量导入规则关联</h4>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold; width: 120px;">导入文件:</td>
                                        <td style="padding: 8px;">
                                            <input type="file" accept=".csv,.xlsx,.json" style="width: 100%; margin-bottom: 10px;">
                                            <div style="font-size: 12px; color: #909399;">支持 CSV、Excel、JSON 格式</div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; font-weight: bold;">导入模式:</td>
                                        <td style="padding: 8px;">
                                            <select style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                                                <option value="append" selected>追加到现有规则</option>
                                                <option value="replace">替换现有规则</option>
                                            </select>
                                        </td>
                                    </tr>
                                </table>
                                <div style="margin-top: 15px; padding: 15px; background: #ecf5ff; border-radius: 4px;">
                                    <div style="font-size: 12px; color: #409EFF;">
                                        <div><strong>CSV格式示例:</strong></div>
                                        <pre style="margin-top: 5px; font-size: 11px;">rule_id,order,operator
rule_basic_switch,1,AND
rule_time_filter,2,AND</pre>
                                    </div>
                                </div>
                            </div>
                        `, function() {
                            alert('批量导入成功！');
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
