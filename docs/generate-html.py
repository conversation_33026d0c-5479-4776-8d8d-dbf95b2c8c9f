#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将markdown文档转换为HTML的简单脚本
需要安装: pip install markdown
"""

import os
import markdown
import re
from pathlib import Path

def convert_md_to_html(md_file, output_dir):
    """将单个markdown文件转换为HTML"""
    
    # 读取markdown文件
    with open(md_file, 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # 配置markdown扩展
    md = markdown.Markdown(extensions=[
        'markdown.extensions.toc',
        'markdown.extensions.tables',
        'markdown.extensions.codehilite',
        'markdown.extensions.fenced_code'
    ])
    
    # 转换为HTML
    html_content = md.convert(md_content)
    
    # 获取文件名（不含扩展名）
    file_name = Path(md_file).stem
    
    # 生成完整的HTML页面
    full_html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{file_name}</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        
        .container {{
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        
        h1, h2, h3, h4, h5, h6 {{
            color: #2c3e50;
            margin-top: 2em;
            margin-bottom: 1em;
        }}
        
        h1 {{
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        
        h2 {{
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
        }}
        
        h3 {{
            color: #34495e;
        }}
        
        code {{
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.9em;
        }}
        
        pre {{
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 20px 0;
        }}
        
        pre code {{
            background: none;
            padding: 0;
            color: inherit;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        
        th {{
            background-color: #f8f9fa;
            font-weight: bold;
        }}
        
        tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        
        blockquote {{
            border-left: 4px solid #3498db;
            margin: 20px 0;
            padding: 10px 20px;
            background-color: #f8f9fa;
        }}
        
        ul, ol {{
            padding-left: 30px;
            margin: 15px 0;
        }}
        
        li {{
            margin: 8px 0;
        }}
        
        a {{
            color: #3498db;
            text-decoration: none;
        }}
        
        a:hover {{
            text-decoration: underline;
        }}
        
        .toc {{
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }}
        
        .toc ul {{
            list-style-type: none;
            padding-left: 20px;
        }}
        
        .toc > ul {{
            padding-left: 0;
        }}
        
        .toc a {{
            color: #495057;
        }}
        
        .back-to-index {{
            position: fixed;
            top: 20px;
            right: 20px;
            background: #3498db;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            text-decoration: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }}
        
        .back-to-index:hover {{
            background: #2980b9;
            color: white;
            text-decoration: none;
        }}
        
        @media (max-width: 768px) {{
            body {{
                padding: 10px;
            }}
            
            .container {{
                padding: 20px;
            }}
            
            .back-to-index {{
                position: static;
                display: inline-block;
                margin-bottom: 20px;
            }}
        }}
    </style>
</head>
<body>
    <a href="index.html" class="back-to-index">← 返回首页</a>
    <div class="container">
        {html_content}
    </div>
</body>
</html>"""
    
    # 输出HTML文件
    output_file = os.path.join(output_dir, f"{file_name}.html")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(full_html)
    
    print(f"已生成: {output_file}")

def main():
    """主函数"""
    docs_dir = Path(__file__).parent
    
    # 查找所有markdown文件
    md_files = list(docs_dir.glob("*.md"))
    
    if not md_files:
        print("未找到markdown文件")
        return
    
    print(f"找到 {len(md_files)} 个markdown文件")
    
    # 转换每个文件
    for md_file in md_files:
        try:
            convert_md_to_html(md_file, docs_dir)
        except Exception as e:
            print(f"转换 {md_file} 时出错: {e}")
    
    print("\n转换完成！")
    print("请打开 index.html 查看文档首页")

if __name__ == "__main__":
    main()
