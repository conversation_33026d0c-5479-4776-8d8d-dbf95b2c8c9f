version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: filter-system-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: filter_system
      MYSQL_USER: filter_user
      MYSQL_PASSWORD: filter123456
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf:/etc/mysql/conf.d
      - ./mysql/init:/docker-entrypoint-initdb.d
      - ./mysql/logs:/var/log/mysql
    command: 
      - --default-authentication-plugin=mysql_native_password
      - --character-set-server=utf8mb4
      - --collation-server=utf8mb4_unicode_ci
      - --explicit_defaults_for_timestamp=true
      - --lower_case_table_names=1
    networks:
      - filter-network

  redis:
    image: redis:7.0-alpine
    container_name: filter-system-redis
    restart: always
    environment:
      TZ: Asia/Shanghai
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/conf/redis.conf:/etc/redis/redis.conf
      - ./redis/logs:/var/log/redis
    command: redis-server /etc/redis/redis.conf
    networks:
      - filter-network

  # 可选：Redis管理界面
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: filter-system-redis-commander
    restart: always
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: admin
      HTTP_PASSWORD: admin123
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - filter-network

  # 可选：MySQL管理界面
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: filter-system-phpmyadmin
    restart: always
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root123456
      MYSQL_ROOT_PASSWORD: root123456
    ports:
      - "8080:80"
    depends_on:
      - mysql
    networks:
      - filter-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  filter-network:
    driver: bridge
