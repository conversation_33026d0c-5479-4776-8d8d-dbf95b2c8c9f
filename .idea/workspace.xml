<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$USER_HOME$/.sdkman/candidates/maven/3.6.3" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="$USER_HOME$/.sdkman/candidates/maven/3.6.3/conf/settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;settings.editor.selected.configurable&quot;: &quot;vcs.Subversion&quot;
  }
}</component>
  <component name="SvnConfiguration">
    <configuration>$USER_HOME$/.subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <created>1753677708149</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753677708149</updated>
      <workItem from="1753677709611" duration="343000" />
    </task>
    <servers />
  </component>
</project>