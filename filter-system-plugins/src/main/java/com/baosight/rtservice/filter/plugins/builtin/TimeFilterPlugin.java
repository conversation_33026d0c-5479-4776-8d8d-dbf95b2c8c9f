package com.baosight.rtservice.filter.plugins.builtin;

import com.baosight.rtservice.filter.core.annotation.Plugin;
import com.baosight.rtservice.filter.core.api.FilterRulePlugin;
import com.baosight.rtservice.filter.core.model.dto.FilterContext;
import com.baosight.rtservice.filter.core.model.dto.FilterResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 时间过滤插件
 * 根据时间段和星期几进行过滤
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
@Plugin(
    id = "time_filter",
    name = "时间过滤插件",
    description = "根据时间段和星期几进行过滤",
    version = "1.0.0",
    author = "RtService Team",
    type = "builtin"
)
public class TimeFilterPlugin implements FilterRulePlugin {
    
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    
    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        try {
            LocalDateTime now = context.getRequestTime() != null ? 
                context.getRequestTime() : LocalDateTime.now();
            
            // 检查星期几
            if (!isValidDayOfWeek(now, config)) {
                return FilterResult.reject("当前星期不在允许范围内");
            }
            
            // 检查时间段
            if (!isValidTimeRange(now, config)) {
                return FilterResult.reject("当前时间不在允许范围内");
            }
            
            return FilterResult.pass();
            
        } catch (Exception e) {
            log.error("时间过滤插件执行异常", e);
            return FilterResult.reject("时间过滤插件执行异常: " + e.getMessage());
        }
    }
    
    /**
     * 检查星期几是否有效
     */
    private boolean isValidDayOfWeek(LocalDateTime dateTime, Map<String, Object> config) {
        Object weekdaysObj = config.get("weekdays");
        if (weekdaysObj == null) {
            return true; // 未配置则不限制
        }
        
        @SuppressWarnings("unchecked")
        List<Integer> weekdays = (List<Integer>) weekdaysObj;
        if (weekdays.isEmpty()) {
            return true;
        }
        
        int dayOfWeek = dateTime.getDayOfWeek().getValue(); // 1=Monday, 7=Sunday
        return weekdays.contains(dayOfWeek);
    }
    
    /**
     * 检查时间段是否有效
     */
    private boolean isValidTimeRange(LocalDateTime dateTime, Map<String, Object> config) {
        String startTimeStr = (String) config.get("startTime");
        String endTimeStr = (String) config.get("endTime");
        
        if (StringUtils.isBlank(startTimeStr) || StringUtils.isBlank(endTimeStr)) {
            return true; // 未配置则不限制
        }
        
        try {
            LocalTime currentTime = dateTime.toLocalTime();
            LocalTime startTime = LocalTime.parse(startTimeStr, TIME_FORMATTER);
            LocalTime endTime = LocalTime.parse(endTimeStr, TIME_FORMATTER);
            
            // 处理跨天的情况
            if (startTime.isAfter(endTime)) {
                // 跨天：例如 22:00 - 06:00
                return currentTime.isAfter(startTime) || currentTime.isBefore(endTime);
            } else {
                // 同一天：例如 09:00 - 18:00
                return !currentTime.isBefore(startTime) && !currentTime.isAfter(endTime);
            }
        } catch (Exception e) {
            log.warn("时间格式解析失败: startTime={}, endTime={}", startTimeStr, endTimeStr, e);
            return true; // 解析失败则不限制
        }
    }
    
    @Override
    public String getPluginId() {
        return "time_filter";
    }
    
    @Override
    public String getPluginName() {
        return "时间过滤插件";
    }
    
    @Override
    public String validateConfig(Map<String, Object> config) {
        // 验证时间格式
        String startTime = (String) config.get("startTime");
        String endTime = (String) config.get("endTime");
        
        if (StringUtils.isNotBlank(startTime)) {
            try {
                LocalTime.parse(startTime, TIME_FORMATTER);
            } catch (Exception e) {
                return "开始时间格式错误，应为 HH:mm 格式";
            }
        }
        
        if (StringUtils.isNotBlank(endTime)) {
            try {
                LocalTime.parse(endTime, TIME_FORMATTER);
            } catch (Exception e) {
                return "结束时间格式错误，应为 HH:mm 格式";
            }
        }
        
        // 验证星期几
        Object weekdaysObj = config.get("weekdays");
        if (weekdaysObj != null) {
            try {
                @SuppressWarnings("unchecked")
                List<Integer> weekdays = (List<Integer>) weekdaysObj;
                for (Integer day : weekdays) {
                    if (day < 1 || day > 7) {
                        return "星期几配置错误，应为1-7的数字（1=周一，7=周日）";
                    }
                }
            } catch (Exception e) {
                return "星期几配置格式错误，应为数字数组";
            }
        }
        
        return null;
    }
    
    @Override
    public String getConfigTemplate() {
        return "{\n" +
               "  \"startTime\": \"09:00\",\n" +
               "  \"endTime\": \"18:00\",\n" +
               "  \"weekdays\": [1, 2, 3, 4, 5]\n" +
               "}";
    }
}
