# Filter System Database Services

这个Docker Compose配置为Filter System 4J项目提供MySQL和Redis数据库服务。

## 🚀 快速启动

### 启动所有服务
```bash
docker-compose up -d
```

### 启动特定服务
```bash
# 只启动MySQL
docker-compose up -d mysql

# 只启动Redis
docker-compose up -d redis

# 启动MySQL和Redis（不包含管理界面）
docker-compose up -d mysql redis
```

### 停止服务
```bash
# 停止所有服务
docker-compose down

# 停止并删除数据卷（谨慎使用）
docker-compose down -v
```

## 📋 服务信息

### MySQL 8.0
- **端口**: 3306
- **Root密码**: root123456
- **数据库**: filter_system
- **用户**: filter_user
- **密码**: filter123456
- **管理界面**: http://localhost:8080 (phpMyAdmin)

### Redis 7.0
- **端口**: 6379
- **无密码**（开发环境）
- **管理界面**: http://localhost:8081 (Redis Commander)
  - 用户名: admin
  - 密码: admin123

## 🔧 配置文件

### MySQL配置
- 配置文件: `mysql/conf/my.cnf`
- 初始化脚本: `mysql/init/`
- 日志目录: `mysql/logs/`

### Redis配置
- 配置文件: `redis/conf/redis.conf`
- 日志目录: `redis/logs/`

## 📊 数据库结构

系统会自动创建以下表：
- `filter_plugin` - 插件表
- `filter_group` - 过滤器组表
- `filter_rule` - 过滤规则表
- `filter_execution_log` - 执行日志表

## 🔍 管理界面

### phpMyAdmin (MySQL管理)
- URL: http://localhost:8080
- 用户名: root
- 密码: root123456

### Redis Commander (Redis管理)
- URL: http://localhost:8081
- 用户名: admin
- 密码: admin123

## 📝 常用命令

### 查看服务状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs mysql
docker-compose logs redis
```

### 进入容器
```bash
# 进入MySQL容器
docker-compose exec mysql bash

# 进入Redis容器
docker-compose exec redis sh
```

### 连接数据库
```bash
# 连接MySQL
docker-compose exec mysql mysql -uroot -proot123456 filter_system

# 连接Redis
docker-compose exec redis redis-cli
```
