@options
@packages
-classpath
'/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-core/target/filter-system-core-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.31/spring-context-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.31/spring-beans-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.31/spring-core-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.31/spring-jcl-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.31/spring-expression-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.31/spring-tx-5.3.31.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.31/spring-aop-5.3.31.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.3.1/mybatis-plus-boot-starter-3.5.3.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.3.1/mybatis-plus-3.5.3.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.3.1/mybatis-plus-extension-3.5.3.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.3.1/mybatis-plus-core-3.5.3.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.3.1/mybatis-plus-annotation-3.5.3.1.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.4/jsqlparser-4.4.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.10/mybatis-3.5.10.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.7/mybatis-spring-2.0.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.7.18/spring-boot-autoconfigure-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.7.18/spring-boot-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.7.18/spring-boot-starter-jdbc-2.7.18.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.31/spring-jdbc-5.3.31.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.5/jackson-databind-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.5/jackson-core-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.5/jackson-annotations-2.13.5.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/32.1.1-jre/guava-32.1.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.33.0/checker-qual-3.33.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.18.0/error_prone_annotations-2.18.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.7.18/spring-boot-starter-validation-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.7.18/spring-boot-starter-2.7.18.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.7.18/spring-boot-starter-logging-2.7.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.12/logback-classic-1.2.12.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.12/logback-core-1.2.12.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.17.2/log4j-to-slf4j-2.17.2.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.2/log4j-api-2.17.2.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.36/jul-to-slf4j-1.7.36.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.30/snakeyaml-1.30.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.83/tomcat-embed-el-9.0.83.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.2.5.Final/hibernate-validator-6.2.5.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar'
-encoding
'UTF-8'
-protected
-source
'8'
-sourcepath
'/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/src/main/java:/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/generated-sources/annotations'
-author
-bottom
'Copyright &#169; 2025. All rights reserved.'
-charset
'UTF-8'
-d
'/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs'
-docencoding
'UTF-8'
-doctitle
'Filter System SDK 1.0.0-SNAPSHOT API'
-linkoffline
'https://docs.oracle.com/javase/8/docs/api' '/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/javadoc-bundle-options'
-use
-version
-windowtitle
'Filter System SDK 1.0.0-SNAPSHOT API'
com.baosight.rtservice.filter.sdk.util
com.baosight.rtservice.filter.sdk.example
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-core/target/filter-system-core-1.0.0-SNAPSHOT.jar = 1754017869000
/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.31/spring-context-5.3.31.jar = 1753935565000
/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.31/spring-beans-5.3.31.jar = 1753935565000
/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.31/spring-core-5.3.31.jar = 1753935565000
/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.31/spring-jcl-5.3.31.jar = 1753935565000
/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.31/spring-expression-5.3.31.jar = 1753935565000
/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.31/spring-tx-5.3.31.jar = 1753935565000
/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.31/spring-aop-5.3.31.jar = 1753935565000
/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.3.1/mybatis-plus-boot-starter-3.5.3.1.jar = 1672384367000
/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.3.1/mybatis-plus-3.5.3.1.jar = 1672384334000
/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.3.1/mybatis-plus-extension-3.5.3.1.jar = 1672384447000
/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.3.1/mybatis-plus-core-3.5.3.1.jar = 1672384418000
/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.3.1/mybatis-plus-annotation-3.5.3.1.jar = 1672384306000
/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.4/jsqlparser-4.4.jar = 1649622856000
/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.10/mybatis-3.5.10.jar = 1653336395000
/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.7/mybatis-spring-2.0.7.jar = 1643453883000
/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.7.18/spring-boot-autoconfigure-2.7.18.jar = 1700726722000
/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.7.18/spring-boot-2.7.18.jar = 1700726727000
/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.7.18/spring-boot-starter-jdbc-2.7.18.jar = 1700726734000
/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar = 1753683880000
/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.31/spring-jdbc-5.3.31.jar = 1753935565000
/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.5/jackson-databind-2.13.5.jar = 1753683876000
/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.5/jackson-core-2.13.5.jar = 1753683876000
/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.5/jackson-annotations-2.13.5.jar = 1674432253000
/Users/<USER>/.m2/repository/com/google/guava/guava/32.1.1-jre/guava-32.1.1-jre.jar = 1688135858000
/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar = 1542650246000
/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar = 1753683880000
/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar = 1753683880000
/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.33.0/checker-qual-3.33.0.jar = 1680540455000
/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.18.0/error_prone_annotations-2.18.0.jar = 1673284883000
/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar = 1673409222000
/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar = 1753683880000
/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.7.18/spring-boot-starter-validation-2.7.18.jar = 1700726724000
/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.7.18/spring-boot-starter-2.7.18.jar = 1700726728000
/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.7.18/spring-boot-starter-logging-2.7.18.jar = 1700726740000
/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.12/logback-classic-1.2.12.jar = 1679606457000
/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.12/logback-core-1.2.12.jar = 1679606411000
/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.17.2/log4j-to-slf4j-2.17.2.jar = 1645648225000
/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.2/log4j-api-2.17.2.jar = 1753683875000
/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.36/jul-to-slf4j-1.7.36.jar = 1753683875000
/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar = 1753683877000
/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.30/snakeyaml-1.30.jar = 1753935566000
/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.83/tomcat-embed-el-9.0.83.jar = 1699566501000
/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.2.5.Final/hibernate-validator-6.2.5.Final.jar = 1662992429000
/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar = 1565473573000
/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar = 1641938053000
/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar = 1571525227000
/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar = 1753683878000
/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar = 1753683875000
/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar = 1753683875000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/src/main/java = 1754015585000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/generated-sources/annotations = 1754017869000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs/constant-values.html = 1754017870000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs/overview-tree.html = 1754017870000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs/index.html = 1754017870000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs/overview-frame.html = 1754017870000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs/allclasses-noframe.html = 1754017870000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs/index-all.html = 1754017870000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs/deprecated-list.html = 1754017870000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs/script.js = 1754017870000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs/stylesheet.css = 1754017870000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs/overview-summary.html = 1754017870000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs/help-doc.html = 1754017870000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs/allclasses-frame.html = 1754017870000
/Users/<USER>/Baosight/Project/Java/filter-system-4j/filter-system-sdk/target/apidocs/package-list = 1754017870000
