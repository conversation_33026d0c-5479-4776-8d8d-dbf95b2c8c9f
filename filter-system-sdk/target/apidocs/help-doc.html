<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_462) on Fri Aug 01 11:11:10 CST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>API 帮助 (Filter System SDK 1.0.0-SNAPSHOT API)</title>
<meta name="date" content="2025-08-01">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="API \u5E2E\u52A9 (Filter System SDK 1.0.0-SNAPSHOT API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-all.html">索引</a></li>
<li class="navBarCell1Rev">帮助</li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?help-doc.html" target="_top">框架</a></li>
<li><a href="help-doc.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">此 API 文档的组织方式</h1>
<div class="subTitle">此 API (应用程序编程接口) 文档包含对应于导航栏中的项目的页面, 如下所述。</div>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<h2>概览</h2>
<p><a href="overview-summary.html">概览</a> 页面是此 API 文档的首页, 提供了所有程序包的列表及其概要。此页面也可能包含这些程序包的总体说明。</p>
</li>
<li class="blockList">
<h2>程序包</h2>
<p>每个程序包都有一个页面, 其中包含它的类和接口的列表及其概要。此页面可以包含六个类别:</p>
<ul>
<li>接口 (斜体)</li>
<li>类</li>
<li>枚举</li>
<li>异常错误</li>
<li>错误</li>
<li>注释类型</li>
</ul>
</li>
<li class="blockList">
<h2>类/接口</h2>
<p>每个类, 接口, 嵌套类和嵌套接口都有各自的页面。其中每个页面都由三部分 (类/接口说明, 概要表, 以及详细的成员说明) 组成:</p>
<ul>
<li>类继承图</li>
<li>直接子类</li>
<li>所有已知子接口</li>
<li>所有已知实现类</li>
<li>类/接口声明</li>
<li>类/接口说明</li>
</ul>
<ul>
<li>嵌套类概要</li>
<li>字段概要</li>
<li>构造器概要</li>
<li>方法概要</li>
</ul>
<ul>
<li>字段详细资料</li>
<li>构造器详细资料</li>
<li>方法详细资料</li>
</ul>
<p>每个概要条目都包含该项目的详细说明的第一句。概要条目按字母顺序排列, 而详细说明则按其在源代码中出现的顺序排列。这样保持了程序员所建立的逻辑分组。</p>
</li>
<li class="blockList">
<h2>注释类型</h2>
<p>每个注释类型都有各自的页面, 其中包含以下部分:</p>
<ul>
<li>注释类型声明</li>
<li>注释类型说明</li>
<li>必需元素概要</li>
<li>可选元素概要</li>
<li>元素详细资料</li>
</ul>
</li>
<li class="blockList">
<h2>枚举</h2>
<p>每个枚举都有各自的页面, 其中包含以下部分:</p>
<ul>
<li>枚举声明</li>
<li>枚举说明</li>
<li>枚举常量概要</li>
<li>枚举常量详细资料</li>
</ul>
</li>
<li class="blockList">
<h2>使用</h2>
<p>每个已文档化的程序包, 类和接口都有各自的“使用”页面。此页面介绍了使用给定类或程序包的任何部分的程序包, 类, 方法, 构造器和字段。对于给定的类或接口 A, 其“使用”页面包含 A 的子类, 声明为 A 的字段, 返回 A 的方法, 以及带有类型为 A 的参数的方法和构造器。访问此页面的方法是: 首先转至程序包, 类或接口, 然后单击导航栏中的 "使用" 链接。</p>
</li>
<li class="blockList">
<h2>树 (类分层结构)</h2>
<p>对于所有程序包, 有一个<a href="overview-tree.html">类分层结构</a>页面, 以及每个程序包的分层结构。每个分层结构页面都包含类的列表和接口的列表。从<code>java.lang.Object</code>开始, 按继承结构对类进行排列。接口不从<code>java.lang.Object</code>继承。</p>
<ul>
<li>查看“概览”页面时, 单击 "树" 将显示所有程序包的分层结构。</li>
<li>查看特定程序包, 类或接口页面时, 单击 "树" 将仅显示该程序包的分层结构。</li>
</ul>
</li>
<li class="blockList">
<h2>已过时的 API</h2>
<p><a href="deprecated-list.html">已过时的 API</a> 页面列出了所有已过时的 API。一般由于进行了改进并且通常提供了替代的 API, 所以建议不要使用已过时的 API。在将来的实现过程中, 可能会删除已过时的 API。</p>
</li>
<li class="blockList">
<h2>索引</h2>
<p><a href="index-all.html">索引</a> 包含按字母顺序排列的所有类, 接口, 构造器, 方法和字段的列表。</p>
</li>
<li class="blockList">
<h2>上一个/下一个</h2>
<p>这些链接使您可以转至下一个或上一个类, 接口, 程序包或相关页面。</p>
</li>
<li class="blockList">
<h2>框架/无框架</h2>
<p>这些链接用于显示和隐藏 HTML 框架。所有页面均具有有框架和无框架两种显示方式。</p>
</li>
<li class="blockList">
<h2>所有类</h2>
<p><a href="allclasses-noframe.html">所有类</a>链接显示所有类和接口 (除了非静态嵌套类型)。</p>
</li>
<li class="blockList">
<h2>序列化表格</h2>
<p>每个可序列化或可外部化的类都有其序列化字段和方法的说明。此信息对重新实现者有用, 而对使用 API 的开发者则没有什么用处。尽管导航栏中没有链接, 但您可以通过下列方式获取此信息: 转至任何序列化类, 然后单击类说明的 "另请参阅" 部分中的 "序列化表格"。</p>
</li>
<li class="blockList">
<h2>常量字段值</h2>
<p><a href="constant-values.html">常量字段值</a>页面列出了静态最终字段及其值。</p>
</li>
</ul>
<span class="emphasizedPhrase">此帮助文件适用于使用标准 doclet 生成的 API 文档。</span></div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-all.html">索引</a></li>
<li class="navBarCell1Rev">帮助</li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?help-doc.html" target="_top">框架</a></li>
<li><a href="help-doc.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</body>
</html>
