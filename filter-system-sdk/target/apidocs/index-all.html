<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_462) on Fri Aug 01 11:11:10 CST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>索引 (Filter System SDK 1.0.0-SNAPSHOT API)</title>
<meta name="date" content="2025-08-01">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7D22\u5F15 (Filter System SDK 1.0.0-SNAPSHOT API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?index-all.html" target="_top">框架</a></li>
<li><a href="index-all.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:V">V</a>&nbsp;<a name="I:C">
<!--   -->
</a>
<h2 class="title">C</h2>
<dl>
<dt><a href="com/baosight/rtservice/filter/sdk/example/package-summary.html">com.baosight.rtservice.filter.sdk.example</a> - 程序包 com.baosight.rtservice.filter.sdk.example</dt>
<dd>&nbsp;</dd>
<dt><a href="com/baosight/rtservice/filter/sdk/util/package-summary.html">com.baosight.rtservice.filter.sdk.util</a> - 程序包 com.baosight.rtservice.filter.sdk.util</dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:D">
<!--   -->
</a>
<h2 class="title">D</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#destroy--">destroy()</a></span> - 类 中的方法com.baosight.rtservice.filter.sdk.example.<a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" title="com.baosight.rtservice.filter.sdk.example中的类">StandardPluginExample</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:E">
<!--   -->
</a>
<h2 class="title">E</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#execute-com.baosight.rtservice.filter.core.model.dto.FilterContext-java.util.Map-">execute(FilterContext, Map&lt;String, Object&gt;)</a></span> - 类 中的方法com.baosight.rtservice.filter.sdk.example.<a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" title="com.baosight.rtservice.filter.sdk.example中的类">StandardPluginExample</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:F">
<!--   -->
</a>
<h2 class="title">F</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#failure-java.lang.String-">failure(String)</a></span> - 类 中的静态方法com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>
<div class="block">创建失败结果</div>
</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#failure-java.lang.String-java.lang.String-">failure(String, String)</a></span> - 类 中的静态方法com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>
<div class="block">创建失败结果（带错误码）</div>
</dd>
</dl>
<a name="I:G">
<!--   -->
</a>
<h2 class="title">G</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getConfigBoolean-java.util.Map-java.lang.String-">getConfigBoolean(Map&lt;String, Object&gt;, String)</a></span> - 类 中的静态方法com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>
<div class="block">获取配置参数（布尔类型）</div>
</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getConfigBoolean-java.util.Map-java.lang.String-java.lang.Boolean-">getConfigBoolean(Map&lt;String, Object&gt;, String, Boolean)</a></span> - 类 中的静态方法com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>
<div class="block">获取配置参数（布尔类型，带默认值）</div>
</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getConfigInteger-java.util.Map-java.lang.String-">getConfigInteger(Map&lt;String, Object&gt;, String)</a></span> - 类 中的静态方法com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>
<div class="block">获取配置参数（整数类型）</div>
</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getConfigInteger-java.util.Map-java.lang.String-java.lang.Integer-">getConfigInteger(Map&lt;String, Object&gt;, String, Integer)</a></span> - 类 中的静态方法com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>
<div class="block">获取配置参数（整数类型，带默认值）</div>
</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getConfigString-java.util.Map-java.lang.String-">getConfigString(Map&lt;String, Object&gt;, String)</a></span> - 类 中的静态方法com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>
<div class="block">获取配置参数（字符串类型）</div>
</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getConfigString-java.util.Map-java.lang.String-java.lang.String-">getConfigString(Map&lt;String, Object&gt;, String, String)</a></span> - 类 中的静态方法com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>
<div class="block">获取配置参数（字符串类型，带默认值）</div>
</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#getConfigTemplate--">getConfigTemplate()</a></span> - 类 中的方法com.baosight.rtservice.filter.sdk.example.<a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" title="com.baosight.rtservice.filter.sdk.example中的类">StandardPluginExample</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getContextAttribute-com.baosight.rtservice.filter.core.model.dto.FilterContext-java.lang.String-java.lang.Class-">getContextAttribute(FilterContext, String, Class&lt;T&gt;)</a></span> - 类 中的静态方法com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>
<div class="block">安全地获取上下文属性</div>
</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#getPluginId--">getPluginId()</a></span> - 类 中的方法com.baosight.rtservice.filter.sdk.example.<a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" title="com.baosight.rtservice.filter.sdk.example中的类">StandardPluginExample</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#getPluginName--">getPluginName()</a></span> - 类 中的方法com.baosight.rtservice.filter.sdk.example.<a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" title="com.baosight.rtservice.filter.sdk.example中的类">StandardPluginExample</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:I">
<!--   -->
</a>
<h2 class="title">I</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#initialize--">initialize()</a></span> - 类 中的方法com.baosight.rtservice.filter.sdk.example.<a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" title="com.baosight.rtservice.filter.sdk.example中的类">StandardPluginExample</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#isHealthy--">isHealthy()</a></span> - 类 中的方法com.baosight.rtservice.filter.sdk.example.<a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" title="com.baosight.rtservice.filter.sdk.example中的类">StandardPluginExample</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#isRoleMatch-com.baosight.rtservice.filter.core.model.dto.FilterContext-java.lang.String-">isRoleMatch(FilterContext, String)</a></span> - 类 中的静态方法com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>
<div class="block">检查用户角色是否匹配</div>
</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#isUserMatch-com.baosight.rtservice.filter.core.model.dto.FilterContext-java.lang.String-">isUserMatch(FilterContext, String)</a></span> - 类 中的静态方法com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>
<div class="block">检查用户是否匹配</div>
</dd>
</dl>
<a name="I:P">
<!--   -->
</a>
<h2 class="title">P</h2>
<dl>
<dt><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类"><span class="typeNameLink">PluginUtils</span></a> - <a href="com/baosight/rtservice/filter/sdk/util/package-summary.html">com.baosight.rtservice.filter.sdk.util</a>中的类</dt>
<dd>
<div class="block">插件工具类
 提供插件开发常用的工具方法</div>
</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#PluginUtils--">PluginUtils()</a></span> - 类 的构造器com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:S">
<!--   -->
</a>
<h2 class="title">S</h2>
<dl>
<dt><a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" title="com.baosight.rtservice.filter.sdk.example中的类"><span class="typeNameLink">StandardPluginExample</span></a> - <a href="com/baosight/rtservice/filter/sdk/example/package-summary.html">com.baosight.rtservice.filter.sdk.example</a>中的类</dt>
<dd>
<div class="block">标准插件示例
 演示如何实现标准接口插件</div>
</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#StandardPluginExample--">StandardPluginExample()</a></span> - 类 的构造器com.baosight.rtservice.filter.sdk.example.<a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" title="com.baosight.rtservice.filter.sdk.example中的类">StandardPluginExample</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#start--">start()</a></span> - 类 中的方法com.baosight.rtservice.filter.sdk.example.<a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" title="com.baosight.rtservice.filter.sdk.example中的类">StandardPluginExample</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#stop--">stop()</a></span> - 类 中的方法com.baosight.rtservice.filter.sdk.example.<a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" title="com.baosight.rtservice.filter.sdk.example中的类">StandardPluginExample</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#success--">success()</a></span> - 类 中的静态方法com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>
<div class="block">创建成功结果</div>
</dd>
</dl>
<a name="I:V">
<!--   -->
</a>
<h2 class="title">V</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#validateConfig-java.util.Map-">validateConfig(Map&lt;String, Object&gt;)</a></span> - 类 中的方法com.baosight.rtservice.filter.sdk.example.<a href="com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" title="com.baosight.rtservice.filter.sdk.example中的类">StandardPluginExample</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html#validateRequiredConfig-java.util.Map-java.lang.String...-">validateRequiredConfig(Map&lt;String, Object&gt;, String...)</a></span> - 类 中的静态方法com.baosight.rtservice.filter.sdk.util.<a href="com/baosight/rtservice/filter/sdk/util/PluginUtils.html" title="com.baosight.rtservice.filter.sdk.util中的类">PluginUtils</a></dt>
<dd>
<div class="block">验证必需的配置参数</div>
</dd>
</dl>
<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:V">V</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?index-all.html" target="_top">框架</a></li>
<li><a href="index-all.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</body>
</html>
