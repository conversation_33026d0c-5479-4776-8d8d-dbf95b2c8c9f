<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_462) on Fri Aug 01 11:11:10 CST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>PluginUtils (Filter System SDK 1.0.0-SNAPSHOT API)</title>
<meta name="date" content="2025-08-01">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PluginUtils (Filter System SDK 1.0.0-SNAPSHOT API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/PluginUtils.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/baosight/rtservice/filter/sdk/util/PluginUtils.html" target="_top">框架</a></li>
<li><a href="PluginUtils.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.baosight.rtservice.filter.sdk.util</div>
<h2 title="类 PluginUtils" class="title">类 PluginUtils</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.baosight.rtservice.filter.sdk.util.PluginUtils</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">PluginUtils</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a></pre>
<div class="block">插件工具类
 提供插件开发常用的工具方法</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0.0</dd>
<dt><span class="simpleTagLabel">作者:</span></dt>
<dd>RtService Team</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#PluginUtils--">PluginUtils</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static com.baosight.rtservice.filter.core.model.dto.FilterResult</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#failure-java.lang.String-">failure</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;reason)</code>
<div class="block">创建失败结果</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static com.baosight.rtservice.filter.core.model.dto.FilterResult</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#failure-java.lang.String-java.lang.String-">failure</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;reason,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;errorCode)</code>
<div class="block">创建失败结果（带错误码）</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="java.lang中的类或接口">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getConfigBoolean-java.util.Map-java.lang.String-">getConfigBoolean</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key)</code>
<div class="block">获取配置参数（布尔类型）</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="java.lang中的类或接口">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getConfigBoolean-java.util.Map-java.lang.String-java.lang.Boolean-">getConfigBoolean</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="java.lang中的类或接口">Boolean</a>&nbsp;defaultValue)</code>
<div class="block">获取配置参数（布尔类型，带默认值）</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="java.lang中的类或接口">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getConfigInteger-java.util.Map-java.lang.String-">getConfigInteger</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key)</code>
<div class="block">获取配置参数（整数类型）</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="java.lang中的类或接口">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getConfigInteger-java.util.Map-java.lang.String-java.lang.Integer-">getConfigInteger</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="java.lang中的类或接口">Integer</a>&nbsp;defaultValue)</code>
<div class="block">获取配置参数（整数类型，带默认值）</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getConfigString-java.util.Map-java.lang.String-">getConfigString</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key)</code>
<div class="block">获取配置参数（字符串类型）</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getConfigString-java.util.Map-java.lang.String-java.lang.String-">getConfigString</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;defaultValue)</code>
<div class="block">获取配置参数（字符串类型，带默认值）</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#getContextAttribute-com.baosight.rtservice.filter.core.model.dto.FilterContext-java.lang.String-java.lang.Class-">getContextAttribute</a></span>(com.baosight.rtservice.filter.core.model.dto.FilterContext&nbsp;context,
                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key,
                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html?is-external=true" title="java.lang中的类或接口">Class</a>&lt;T&gt;&nbsp;type)</code>
<div class="block">安全地获取上下文属性</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#isRoleMatch-com.baosight.rtservice.filter.core.model.dto.FilterContext-java.lang.String-">isRoleMatch</a></span>(com.baosight.rtservice.filter.core.model.dto.FilterContext&nbsp;context,
           <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;targetRole)</code>
<div class="block">检查用户角色是否匹配</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#isUserMatch-com.baosight.rtservice.filter.core.model.dto.FilterContext-java.lang.String-">isUserMatch</a></span>(com.baosight.rtservice.filter.core.model.dto.FilterContext&nbsp;context,
           <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;targetUserId)</code>
<div class="block">检查用户是否匹配</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static com.baosight.rtservice.filter.core.model.dto.FilterResult</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#success--">success</a></span>()</code>
<div class="block">创建成功结果</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/util/PluginUtils.html#validateRequiredConfig-java.util.Map-java.lang.String...-">validateRequiredConfig</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>...&nbsp;requiredKeys)</code>
<div class="block">验证必需的配置参数</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="java.lang中的类或接口">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="java.lang中的类或接口">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="java.lang中的类或接口">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="java.lang中的类或接口">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="java.lang中的类或接口">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="java.lang中的类或接口">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="java.lang中的类或接口">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="java.lang中的类或接口">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="java.lang中的类或接口">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="java.lang中的类或接口">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="java.lang中的类或接口">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="PluginUtils--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PluginUtils</h4>
<pre>public&nbsp;PluginUtils()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getConfigString-java.util.Map-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigString</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;getConfigString(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
                                     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key)</pre>
<div class="block">获取配置参数（字符串类型）</div>
</li>
</ul>
<a name="getConfigString-java.util.Map-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigString</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;getConfigString(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
                                     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key,
                                     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;defaultValue)</pre>
<div class="block">获取配置参数（字符串类型，带默认值）</div>
</li>
</ul>
<a name="getConfigInteger-java.util.Map-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigInteger</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="java.lang中的类或接口">Integer</a>&nbsp;getConfigInteger(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
                                       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key)</pre>
<div class="block">获取配置参数（整数类型）</div>
</li>
</ul>
<a name="getConfigInteger-java.util.Map-java.lang.String-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigInteger</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="java.lang中的类或接口">Integer</a>&nbsp;getConfigInteger(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
                                       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key,
                                       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="java.lang中的类或接口">Integer</a>&nbsp;defaultValue)</pre>
<div class="block">获取配置参数（整数类型，带默认值）</div>
</li>
</ul>
<a name="getConfigBoolean-java.util.Map-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigBoolean</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="java.lang中的类或接口">Boolean</a>&nbsp;getConfigBoolean(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
                                       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key)</pre>
<div class="block">获取配置参数（布尔类型）</div>
</li>
</ul>
<a name="getConfigBoolean-java.util.Map-java.lang.String-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigBoolean</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="java.lang中的类或接口">Boolean</a>&nbsp;getConfigBoolean(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
                                       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key,
                                       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="java.lang中的类或接口">Boolean</a>&nbsp;defaultValue)</pre>
<div class="block">获取配置参数（布尔类型，带默认值）</div>
</li>
</ul>
<a name="validateRequiredConfig-java.util.Map-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>validateRequiredConfig</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;validateRequiredConfig(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config,
                                            <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>...&nbsp;requiredKeys)</pre>
<div class="block">验证必需的配置参数</div>
</li>
</ul>
<a name="success--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>success</h4>
<pre>public static&nbsp;com.baosight.rtservice.filter.core.model.dto.FilterResult&nbsp;success()</pre>
<div class="block">创建成功结果</div>
</li>
</ul>
<a name="failure-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>failure</h4>
<pre>public static&nbsp;com.baosight.rtservice.filter.core.model.dto.FilterResult&nbsp;failure(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;reason)</pre>
<div class="block">创建失败结果</div>
</li>
</ul>
<a name="failure-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>failure</h4>
<pre>public static&nbsp;com.baosight.rtservice.filter.core.model.dto.FilterResult&nbsp;failure(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;reason,
                                                                                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;errorCode)</pre>
<div class="block">创建失败结果（带错误码）</div>
</li>
</ul>
<a name="isUserMatch-com.baosight.rtservice.filter.core.model.dto.FilterContext-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isUserMatch</h4>
<pre>public static&nbsp;boolean&nbsp;isUserMatch(com.baosight.rtservice.filter.core.model.dto.FilterContext&nbsp;context,
                                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;targetUserId)</pre>
<div class="block">检查用户是否匹配</div>
</li>
</ul>
<a name="isRoleMatch-com.baosight.rtservice.filter.core.model.dto.FilterContext-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRoleMatch</h4>
<pre>public static&nbsp;boolean&nbsp;isRoleMatch(com.baosight.rtservice.filter.core.model.dto.FilterContext&nbsp;context,
                                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;targetRole)</pre>
<div class="block">检查用户角色是否匹配</div>
</li>
</ul>
<a name="getContextAttribute-com.baosight.rtservice.filter.core.model.dto.FilterContext-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getContextAttribute</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;getContextAttribute(com.baosight.rtservice.filter.core.model.dto.FilterContext&nbsp;context,
                                        <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;key,
                                        <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html?is-external=true" title="java.lang中的类或接口">Class</a>&lt;T&gt;&nbsp;type)</pre>
<div class="block">安全地获取上下文属性</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/PluginUtils.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/baosight/rtservice/filter/sdk/util/PluginUtils.html" target="_top">框架</a></li>
<li><a href="PluginUtils.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</body>
</html>
