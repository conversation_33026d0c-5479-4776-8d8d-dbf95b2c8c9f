<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_462) on Fri Aug 01 11:11:10 CST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>StandardPluginExample (Filter System SDK 1.0.0-SNAPSHOT API)</title>
<meta name="date" content="2025-08-01">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="StandardPluginExample (Filter System SDK 1.0.0-SNAPSHOT API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/StandardPluginExample.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" target="_top">框架</a></li>
<li><a href="StandardPluginExample.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.baosight.rtservice.filter.sdk.example</div>
<h2 title="类 StandardPluginExample" class="title">类 StandardPluginExample</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.baosight.rtservice.filter.sdk.example.StandardPluginExample</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>com.baosight.rtservice.filter.core.api.FilterRulePlugin, com.baosight.rtservice.filter.core.api.PluginLifecycle</dd>
</dl>
<hr>
<br>
<pre>@Plugin(id="example_standard_plugin",
        name="\u6807\u51c6\u63d2\u4ef6\u793a\u4f8b",
        description="\u6f14\u793a\u6807\u51c6\u63a5\u53e3\u63d2\u4ef6\u7684\u5b9e\u73b0\u65b9\u5f0f",
        version="1.0.0",
        author="RtService Team",
        type="custom")
public class <span class="typeNameLabel">StandardPluginExample</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>
implements com.baosight.rtservice.filter.core.api.FilterRulePlugin, com.baosight.rtservice.filter.core.api.PluginLifecycle</pre>
<div class="block">标准插件示例
 演示如何实现标准接口插件</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0.0</dd>
<dt><span class="simpleTagLabel">作者:</span></dt>
<dd>RtService Team</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#StandardPluginExample--">StandardPluginExample</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#destroy--">destroy</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>com.baosight.rtservice.filter.core.model.dto.FilterResult</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#execute-com.baosight.rtservice.filter.core.model.dto.FilterContext-java.util.Map-">execute</a></span>(com.baosight.rtservice.filter.core.model.dto.FilterContext&nbsp;context,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#getConfigTemplate--">getConfigTemplate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#getPluginId--">getPluginId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#getPluginName--">getPluginName</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#initialize--">initialize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#isHealthy--">isHealthy</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#start--">start</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#stop--">stop</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html#validateConfig-java.util.Map-">validateConfig</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="java.lang中的类或接口">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="java.lang中的类或接口">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="java.lang中的类或接口">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="java.lang中的类或接口">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="java.lang中的类或接口">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="java.lang中的类或接口">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="java.lang中的类或接口">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="java.lang中的类或接口">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="java.lang中的类或接口">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="java.lang中的类或接口">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="java.lang中的类或接口">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.baosight.rtservice.filter.core.api.FilterRulePlugin">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;com.baosight.rtservice.filter.core.api.FilterRulePlugin</h3>
<code>getVersion</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="StandardPluginExample--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>StandardPluginExample</h4>
<pre>public&nbsp;StandardPluginExample()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="execute-com.baosight.rtservice.filter.core.model.dto.FilterContext-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>execute</h4>
<pre>public&nbsp;com.baosight.rtservice.filter.core.model.dto.FilterResult&nbsp;execute(com.baosight.rtservice.filter.core.model.dto.FilterContext&nbsp;context,
                                                                         <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>execute</code>&nbsp;在接口中&nbsp;<code>com.baosight.rtservice.filter.core.api.FilterRulePlugin</code></dd>
</dl>
</li>
</ul>
<a name="getPluginId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPluginId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;getPluginId()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>getPluginId</code>&nbsp;在接口中&nbsp;<code>com.baosight.rtservice.filter.core.api.FilterRulePlugin</code></dd>
</dl>
</li>
</ul>
<a name="getPluginName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPluginName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;getPluginName()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>getPluginName</code>&nbsp;在接口中&nbsp;<code>com.baosight.rtservice.filter.core.api.FilterRulePlugin</code></dd>
</dl>
</li>
</ul>
<a name="validateConfig-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>validateConfig</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;validateConfig(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="java.util中的类或接口">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="java.lang中的类或接口">Object</a>&gt;&nbsp;config)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>validateConfig</code>&nbsp;在接口中&nbsp;<code>com.baosight.rtservice.filter.core.api.FilterRulePlugin</code></dd>
</dl>
</li>
</ul>
<a name="getConfigTemplate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigTemplate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="java.lang中的类或接口">String</a>&nbsp;getConfigTemplate()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>getConfigTemplate</code>&nbsp;在接口中&nbsp;<code>com.baosight.rtservice.filter.core.api.FilterRulePlugin</code></dd>
</dl>
</li>
</ul>
<a name="initialize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initialize</h4>
<pre>public&nbsp;void&nbsp;initialize()
                throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="java.lang中的类或接口">Exception</a></pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>initialize</code>&nbsp;在接口中&nbsp;<code>com.baosight.rtservice.filter.core.api.PluginLifecycle</code></dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="java.lang中的类或接口">Exception</a></code></dd>
</dl>
</li>
</ul>
<a name="destroy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>destroy</h4>
<pre>public&nbsp;void&nbsp;destroy()
             throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="java.lang中的类或接口">Exception</a></pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>destroy</code>&nbsp;在接口中&nbsp;<code>com.baosight.rtservice.filter.core.api.PluginLifecycle</code></dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="java.lang中的类或接口">Exception</a></code></dd>
</dl>
</li>
</ul>
<a name="start--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>start</h4>
<pre>public&nbsp;void&nbsp;start()
           throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="java.lang中的类或接口">Exception</a></pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>start</code>&nbsp;在接口中&nbsp;<code>com.baosight.rtservice.filter.core.api.PluginLifecycle</code></dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="java.lang中的类或接口">Exception</a></code></dd>
</dl>
</li>
</ul>
<a name="stop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stop</h4>
<pre>public&nbsp;void&nbsp;stop()
          throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="java.lang中的类或接口">Exception</a></pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>stop</code>&nbsp;在接口中&nbsp;<code>com.baosight.rtservice.filter.core.api.PluginLifecycle</code></dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="java.lang中的类或接口">Exception</a></code></dd>
</dl>
</li>
</ul>
<a name="isHealthy--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isHealthy</h4>
<pre>public&nbsp;boolean&nbsp;isHealthy()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>isHealthy</code>&nbsp;在接口中&nbsp;<code>com.baosight.rtservice.filter.core.api.PluginLifecycle</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/StandardPluginExample.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/baosight/rtservice/filter/sdk/example/StandardPluginExample.html" target="_top">框架</a></li>
<li><a href="StandardPluginExample.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</body>
</html>
