package com.baosight.rtservice.filter.sdk.example;

import com.baosight.rtservice.filter.core.annotation.Plugin;
import com.baosight.rtservice.filter.core.api.FilterRulePlugin;
import com.baosight.rtservice.filter.core.api.PluginLifecycle;
import com.baosight.rtservice.filter.core.model.dto.FilterContext;
import com.baosight.rtservice.filter.core.model.dto.FilterResult;
import com.baosight.rtservice.filter.sdk.util.PluginUtils;

import java.util.Map;

/**
 * 标准插件示例
 * 演示如何实现标准接口插件
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Plugin(
    id = "example_standard_plugin",
    name = "标准插件示例",
    description = "演示标准接口插件的实现方式",
    version = "1.0.0",
    author = "RtService Team",
    type = "custom"
)
public class StandardPluginExample implements FilterRulePlugin, PluginLifecycle {
    
    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        try {
            // 1. 验证配置参数
            String validationError = validateConfig(config);
            if (validationError != null) {
                return PluginUtils.failure("配置验证失败: " + validationError);
            }
            
            // 2. 获取配置参数
            String targetUser = PluginUtils.getConfigString(config, "targetUser");
            Boolean allowAll = PluginUtils.getConfigBoolean(config, "allowAll", false);
            
            // 3. 执行业务逻辑
            if (allowAll) {
                return PluginUtils.success();
            }
            
            if (PluginUtils.isUserMatch(context, targetUser)) {
                return PluginUtils.success();
            }
            
            return PluginUtils.failure("用户不在允许列表中");
            
        } catch (Exception e) {
            return PluginUtils.failure("插件执行异常: " + e.getMessage());
        }
    }
    
    @Override
    public String getPluginId() {
        return "example_standard_plugin";
    }
    
    @Override
    public String getPluginName() {
        return "标准插件示例";
    }
    
    @Override
    public String validateConfig(Map<String, Object> config) {
        // 验证必需参数
        Boolean allowAll = PluginUtils.getConfigBoolean(config, "allowAll", false);
        if (!allowAll) {
            return PluginUtils.validateRequiredConfig(config, "targetUser");
        }
        return null;
    }
    
    @Override
    public String getConfigTemplate() {
        return "{\n" +
               "  \"allowAll\": false,\n" +
               "  \"targetUser\": \"user123\"\n" +
               "}";
    }
    
    @Override
    public void initialize() throws Exception {
        System.out.println("标准插件示例初始化");
    }
    
    @Override
    public void destroy() throws Exception {
        System.out.println("标准插件示例销毁");
    }
    
    @Override
    public void start() throws Exception {
        System.out.println("标准插件示例启动");
    }
    
    @Override
    public void stop() throws Exception {
        System.out.println("标准插件示例停止");
    }
    
    @Override
    public boolean isHealthy() {
        return true;
    }
}
