package com.baosight.rtservice.filter.sdk.util;

import com.baosight.rtservice.filter.core.model.dto.FilterContext;
import com.baosight.rtservice.filter.core.model.dto.FilterResult;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * 插件工具类
 * 提供插件开发常用的工具方法
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class PluginUtils {
    
    /**
     * 获取配置参数（字符串类型）
     */
    public static String getConfigString(Map<String, Object> config, String key) {
        return getConfigString(config, key, null);
    }
    
    /**
     * 获取配置参数（字符串类型，带默认值）
     */
    public static String getConfigString(Map<String, Object> config, String key, String defaultValue) {
        if (config == null || !config.containsKey(key)) {
            return defaultValue;
        }
        Object value = config.get(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * 获取配置参数（整数类型）
     */
    public static Integer getConfigInteger(Map<String, Object> config, String key) {
        return getConfigInteger(config, key, null);
    }
    
    /**
     * 获取配置参数（整数类型，带默认值）
     */
    public static Integer getConfigInteger(Map<String, Object> config, String key, Integer defaultValue) {
        if (config == null || !config.containsKey(key)) {
            return defaultValue;
        }
        Object value = config.get(key);
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * 获取配置参数（布尔类型）
     */
    public static Boolean getConfigBoolean(Map<String, Object> config, String key) {
        return getConfigBoolean(config, key, null);
    }
    
    /**
     * 获取配置参数（布尔类型，带默认值）
     */
    public static Boolean getConfigBoolean(Map<String, Object> config, String key, Boolean defaultValue) {
        if (config == null || !config.containsKey(key)) {
            return defaultValue;
        }
        Object value = config.get(key);
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return Boolean.valueOf(value.toString());
    }
    
    /**
     * 验证必需的配置参数
     */
    public static String validateRequiredConfig(Map<String, Object> config, String... requiredKeys) {
        if (config == null) {
            return "配置参数不能为空";
        }
        
        for (String key : requiredKeys) {
            if (!config.containsKey(key) || config.get(key) == null) {
                return "缺少必需的配置参数: " + key;
            }
            
            // 检查字符串是否为空
            Object value = config.get(key);
            if (value instanceof String && StringUtils.isBlank((String) value)) {
                return "配置参数不能为空: " + key;
            }
        }
        
        return null;
    }
    
    /**
     * 创建成功结果
     */
    public static FilterResult success() {
        return FilterResult.pass();
    }
    
    /**
     * 创建失败结果
     */
    public static FilterResult failure(String reason) {
        return FilterResult.reject(reason);
    }
    
    /**
     * 创建失败结果（带错误码）
     */
    public static FilterResult failure(String reason, String errorCode) {
        return FilterResult.reject(reason, errorCode);
    }
    
    /**
     * 检查用户是否匹配
     */
    public static boolean isUserMatch(FilterContext context, String targetUserId) {
        if (StringUtils.isBlank(targetUserId)) {
            return true;
        }
        return targetUserId.equals(context.getUserId());
    }
    
    /**
     * 检查用户角色是否匹配
     */
    public static boolean isRoleMatch(FilterContext context, String targetRole) {
        if (StringUtils.isBlank(targetRole)) {
            return true;
        }
        return targetRole.equals(context.getUserRole());
    }
    
    /**
     * 安全地获取上下文属性
     */
    public static <T> T getContextAttribute(FilterContext context, String key, Class<T> type) {
        if (context == null || StringUtils.isBlank(key)) {
            return null;
        }
        
        Object value = context.getAttribute(key);
        if (value == null) {
            return null;
        }
        
        if (type.isInstance(value)) {
            return type.cast(value);
        }
        
        return null;
    }
}
