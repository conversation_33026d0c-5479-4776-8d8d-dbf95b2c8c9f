# Filter System 4J - 插件式过滤系统

## 🎯 项目概述

Filter System 4J 是一个基于Java的插件式过滤系统，旨在替代现有的简单勿扰模式配置，提供更加灵活、可扩展的过滤能力。系统支持标准接口和自定义方法两种插件实现方式，通过反射适配器实现统一调用。

## 🏗️ 项目结构

```
filter-system-4j/
├── pom.xml                           # 父POM文件
├── filter-system-core/               # 核心模块
│   ├── src/main/java/
│   │   └── com/baosight/rtservice/filter/core/
│   │       ├── annotation/           # 注解定义
│   │       ├── api/                  # 核心接口
│   │       ├── model/                # 数据模型
│   │       └── exception/            # 异常定义
│   └── pom.xml
├── filter-system-plugins/            # 内置插件模块
│   ├── src/main/java/
│   │   └── com/baosight/rtservice/filter/plugins/
│   │       └── builtin/              # 内置插件
│   └── pom.xml
├── filter-system-sdk/                # 插件开发SDK
│   ├── src/main/java/
│   │   └── com/baosight/rtservice/filter/sdk/
│   │       ├── util/                 # SDK工具类
│   │       └── example/              # 示例代码
│   └── pom.xml
├── filter-system-web/                # Web管理界面
│   ├── src/main/java/
│   │   └── com/baosight/rtservice/filter/web/
│   ├── src/main/resources/
│   │   └── application.yml           # 配置文件
│   └── pom.xml
├── filter-system-examples/           # 示例项目
│   └── pom.xml
└── docs/                             # 技术文档
    ├── README.md
    ├── 01-需求分析-插件式过滤系统需求分析.md
    ├── 02-架构设计-插件式过滤系统架构设计.md
    └── ...
```

## 🔧 技术栈

- **Java**: 8+
- **Spring Boot**: 2.7.18
- **MyBatis Plus**: *******
- **MySQL**: 8.0+
- **Maven**: 3.6+
- **Lombok**: 1.18.28

## 🚀 快速开始

### 1. 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 8.0+

### 2. 克隆项目

```bash
git clone <repository-url>
cd filter-system-4j
```

### 3. 编译项目

```bash
mvn clean compile
```

### 4. 运行测试

```bash
mvn test
```

### 5. 启动应用

```bash
cd filter-system-web
mvn spring-boot:run
```

应用将在 http://localhost:8080/filter-system 启动

## 📋 核心特性

### 1. 插件化架构
- ✅ 支持自定义过滤规则插件
- ✅ 基于Java SPI机制
- ✅ 热插拔支持
- ✅ 插件隔离和异常处理

### 2. 规则编排
- ✅ 支持与或非逻辑组合
- ✅ 短路执行优化
- ✅ 执行顺序控制
- ✅ 超时和重试机制

### 3. 配置管理
- ✅ 简化的表格表单配置
- ✅ 动态配置生成
- ✅ 配置验证和测试
- ✅ 版本管理和回滚

### 4. 监控统计
- ✅ 实时执行统计
- ✅ 性能监控
- ✅ 错误日志分析
- ✅ 告警通知

## 🔌 插件开发

### 标准接口插件

```java
@Plugin(
    id = "my_plugin",
    name = "我的插件",
    description = "插件描述",
    version = "1.0.0"
)
public class MyPlugin implements FilterRulePlugin {
    
    @Override
    public FilterResult execute(FilterContext context, Map<String, Object> config) {
        // 插件逻辑
        return FilterResult.pass();
    }
    
    @Override
    public String getPluginId() {
        return "my_plugin";
    }
    
    @Override
    public String getPluginName() {
        return "我的插件";
    }
}
```

### 自定义方法插件

```java
@Plugin(
    id = "custom_method_plugin",
    name = "自定义方法插件"
)
public class CustomMethodPlugin {
    
    @PluginMethod(name = "customFilter", primary = true)
    public boolean customFilter(String userId, Map<String, Object> config) {
        // 自定义逻辑
        return true;
    }
}
```

## 📚 文档

详细的技术文档请查看 [docs](./docs) 目录：

- [需求分析](./docs/01-需求分析-插件式过滤系统需求分析.md)
- [架构设计](./docs/02-架构设计-插件式过滤系统架构设计.md)
- [数据库设计](./docs/03-数据库设计-插件式过滤系统数据库设计.md)
- [插件开发规范](./docs/04-插件开发规范-插件开发指南与集成规范.md)
- [界面设计](./docs/05-界面设计-配置管理界面设计.md)
- [实施计划](./docs/06-实施计划-分阶段实施计划与技术方案.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📄 许可证

本项目归宝信软件所有，仅供内部使用。

## 📞 联系我们

- **开发团队**: RtService团队
- **技术支持**: 通过项目管理系统提交问题
