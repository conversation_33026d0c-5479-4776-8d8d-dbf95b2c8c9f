<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baosight.rtservice</groupId>
        <artifactId>filter-system-4j</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>filter-system-examples</artifactId>
    <name>Filter System Examples</name>
    <description>过滤系统示例和演示代码</description>

    <dependencies>
        <!-- SDK模块 -->
        <dependency>
            <groupId>com.baosight.rtservice</groupId>
            <artifactId>filter-system-sdk</artifactId>
        </dependency>
        
        <!-- 核心模块 -->
        <dependency>
            <groupId>com.baosight.rtservice</groupId>
            <artifactId>filter-system-core</artifactId>
        </dependency>
        
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <!-- 日志 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        
        <!-- 工具库 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        
        <!-- 测试 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
